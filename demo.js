/**
 * UAUI System Demo
 * Simple script to test the UAUI system with different providers
 */

const { UAUI, createUAUIFromExisting } = require('./dist/uaui');

// Load environment variables (optional)
try {
  require('dotenv').config();
} catch (e) {
  console.log('dotenv not installed, skipping .env loading');
}

// Configure AI providers from environment or with dummy keys
const providers = [
  {
    id: 'openai-1',
    name: 'OpenAI GPT-4',
    type: 'openai',
    apiKey: process.env.OPENAI_API_KEY || 'YOUR_OPENAI_API_KEY',
    defaultModel: 'gpt-4',
    maxTokens: 4000,
    temperature: 0.7,
    timeout: 30000
  },
  {
    id: 'groq-1',
    name: 'Groq Llama3',
    type: 'groq',
    apiKey: process.env.GROQ_API_KEY || 'YOUR_GROQ_API_KEY',
    defaultModel: 'llama3-70b-8192',
    maxTokens: 4000,
    temperature: 0.7,
    timeout: 30000
  }
];

async function runDemo() {
  console.log('🚀 Starting UAUI demo...');

  // Create UAUI instance
  const uaui = createUAUIFromExisting(providers, {
    environment: 'development',
    logLevel: 'debug',
    selectionStrategy: 'smart'
  });

  try {
    // Initialize UAUI
    console.log('Initializing UAUI...');
    await uaui.initialize();
    console.log('✅ UAUI initialized successfully');

    // Get available providers
    const availableProviders = await uaui.getAvailableProviders();
    console.log(`Available providers (${availableProviders.length}):`, availableProviders);

    // Process a test request
    if (availableProviders.length > 0) {
      console.log('\n📝 Processing test request...');

      const request = {
        id: `demo-${Date.now()}`,
        type: 'ai.request',
        message: 'Explain what UAUI protocol is in one sentence.',
        context: {
          app: 'demo',
          userId: 'demo-user',
          sessionId: 'demo-session',
          timestamp: Date.now()
        }
      };

      console.log('Request:', request);

      try {
        const response = await uaui.processAIRequest(request);
        console.log('\n✨ Response received:');
        console.log('Message:', response.message);
        console.log('Provider:', response.metadata.provider);
        console.log('Response time:', response.metadata.responseTime, 'ms');
        console.log('Actions:', response.actions?.length || 0);

        if (response.actions && response.actions.length > 0) {
          console.log('\nActions:', JSON.stringify(response.actions, null, 2));
        }
      } catch (error) {
        console.error('❌ Error processing request:', error.message);
      }
    } else {
      console.warn('⚠️ No providers available. Please check your API keys.');
    }

    // Shutdown UAUI
    console.log('\n🔄 Shutting down UAUI...');
    await uaui.shutdown();
    console.log('✅ UAUI shutdown successfully');

  } catch (error) {
    console.error('❌ Demo failed:', error);
  }
}

// Run the demo
runDemo().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});

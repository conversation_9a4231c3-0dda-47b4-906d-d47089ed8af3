/**
 * UAUI System Test Page
 * Direct testing interface for UAUI functionality
 */

"use client";

import React, { useState, useEffect } from 'react';
import { FullDashboardLayoutWithUAUI } from '@/components/dashboard/EnhancedDashboardLayout';
import { useUA<PERSON> } from '../../uaui';
import { useAIProviderStore } from '@/stores/ai-provider-store';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
    Sparkles,
    Send,
    RefreshCw,
    CheckCircle,
    AlertCircle,
    Bot,
    Zap,
    Brain,
    Clock,
    ArrowLeft
} from 'lucide-react';
import Link from 'next/link';

interface TestMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: number;
    metadata?: {
        provider?: string;
        model?: string;
        responseTime?: number;
        tokens?: number;
    };
    actions?: any[];
}

export default function UAUISystemTestPage() {
    const {
        isInitialized,
        isLoading,
        error,
        availableProviders,
        initialize,
        sendMessage,
        reinitialize
    } = useUAUI();

    const { providers } = useAIProviderStore();
    const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];

    const [messages, setMessages] = useState<TestMessage[]>([
        {
            id: 'welcome',
            role: 'system',
            content: 'Welcome to the UAUI System Testing Interface. Send a message to test AI provider integration.',
            timestamp: Date.now()
        }
    ]);
    const [inputMessage, setInputMessage] = useState('');
    const [isProcessing, setIsProcessing] = useState(false);
    const [selectedWidgetId, setSelectedWidgetId] = useState('test-widget-1');
    const [selectedTest, setSelectedTest] = useState('chat');

    const handleSendMessage = async () => {
        if (!inputMessage.trim() || isProcessing || !isInitialized) return;

        const userMessage: TestMessage = {
            id: `user-${Date.now()}`,
            role: 'user',
            content: inputMessage.trim(),
            timestamp: Date.now()
        };

        setMessages(prev => [...prev, userMessage]);
        setInputMessage('');
        setIsProcessing(true);

        try {
            // Process with UAUI
            const response = await processChatMessage({
                id: `test-${Date.now()}`,
                message: userMessage.content,
                widgetId: selectedWidgetId,
                userId: 'test-user',
                sessionId: `test-session-${Date.now()}`
            });

            if (response) {
                // Add assistant response
                const assistantMessage: TestMessage = {
                    id: `assistant-${Date.now()}`,
                    role: 'assistant',
                    content: response?.message || '',
                    timestamp: Date.now(),
                    metadata: {
                        provider: response.metadata.provider,
                        model: response.metadata.model,
                        responseTime: response.metadata.responseTime,
                        tokens: response.metadata.tokens
                    },
                    actions: response?.actions
                };

                setMessages(prev => [...prev, assistantMessage]);
                console.log('UAUI Response:', response);
            }
        } catch (err: any) {
            const errorMessage: TestMessage = {
                id: `error-${Date.now()}`,
                role: 'system',
                content: `Error: ${err.message || 'Unknown error occurred'}`,
                timestamp: Date.now()
            };
            setMessages(prev => [...prev, errorMessage]);
            console.error('UAUI Test Error:', err);
        } finally {
            setIsProcessing(false);
        }
    };

    // Sample test widget IDs
    const testWidgets = [
        { id: 'test-widget-1', name: 'Test Widget 1' },
        { id: 'test-widget-2', name: 'Test Widget 2' },
        { id: 'demo-widget', name: 'Demo Widget' }
    ];

    // Sample test prompts
    const testPrompts = [
        {
            category: 'General',
            prompts: [
                'Tell me about the UAUI protocol',
                'What kind of AI providers do you support?',
                'Give me a brief introduction to AI assistants'
            ]
        },
        {
            category: 'Widget Actions',
            prompts: [
                'Change the widget color to blue',
                'Make the widget more professional looking',
                'Update the widget to have a friendly tone'
            ]
        },
        {
            category: 'Performance Tests',
            prompts: [
                'I need a really quick response',
                'Can you analyze this complex data set?',
                'Compare the performance of different AI models'
            ]
        }
    ];

    const handleUsePrompt = (prompt: string) => {
        setInputMessage(prompt);
    };

    const handleClearConversation = () => {
        setMessages([
            {
                id: 'welcome',
                role: 'system',
                content: 'Welcome to the UAUI System Testing Interface. Send a message to test AI provider integration.',
                timestamp: Date.now()
            }
        ]);
    };

    // Handle Enter key press
    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };

    return (
        <FullDashboardLayoutWithUAUI
            pageTitle="UAUI System Test"
            pageDescription="Direct testing interface for UAUI functionality"
            headerActions={
                <div className="flex items-center gap-2">
                    <Link href="/dashboard/uaui-system">
                        <Button variant="outline" size="sm">
                            <ArrowLeft className="h-4 w-4 mr-2" />
                            Back to UAUI Dashboard
                        </Button>
                    </Link>
                    <Button onClick={reinitialize} size="sm" disabled={isLoading}>
                        <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                        Reinitialize
                    </Button>
                </div>
            }
        >
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                {/* Test Interface */}
                <div className="lg:col-span-8 space-y-4">
                    <Card className="h-[600px] flex flex-col">
                        <CardHeader className="pb-3 flex flex-row justify-between items-center">
                            <CardTitle className="text-lg font-semibold flex items-center">
                                <Brain className="h-5 w-5 text-primary mr-2" />
                                UAUI Test Console
                                {isInitialized && (
                                    <Badge variant="outline" className="ml-2">
                                        {availableProviders.length} Providers
                                    </Badge>
                                )}
                            </CardTitle>
                            <div className="flex items-center gap-2">
                                <Select value={selectedWidgetId} onValueChange={setSelectedWidgetId}>
                                    <SelectTrigger className="w-[180px]">
                                        <SelectValue placeholder="Select Widget" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {testWidgets.map(widget => (
                                            <SelectItem key={widget.id} value={widget.id}>
                                                {widget.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={handleClearConversation}
                                >
                                    Clear
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent className="flex-1 overflow-hidden flex flex-col">
                            {/* Status Alert */}
                            {!isInitialized && !isLoading && (
                                <Alert className="mb-3">
                                    <Sparkles className="h-4 w-4" />
                                    <AlertDescription>
                                        UAUI is not initialized. Click the Initialize button to start.
                                    </AlertDescription>
                                </Alert>
                            )}

                            {error && (
                                <Alert variant="destructive" className="mb-3">
                                    <AlertCircle className="h-4 w-4" />
                                    <AlertDescription>
                                        {error}
                                    </AlertDescription>
                                </Alert>
                            )}

                            {/* Messages Container */}
                            <div className="flex-1 overflow-y-auto mb-4 p-1 space-y-4">
                                {messages.map((message) => (
                                    <div
                                        key={message.id}
                                        className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                                    >
                                        <div
                                            className={`max-w-[85%] rounded-lg p-3 ${message.role === 'user'
                                                ? 'bg-primary text-primary-foreground'
                                                : message.role === 'system'
                                                    ? 'bg-muted text-muted-foreground'
                                                    : 'bg-secondary text-secondary-foreground'
                                                }`}
                                        >
                                            <p className="text-sm whitespace-pre-wrap">{message.content}</p>

                                            {message.metadata && (
                                                <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700 text-xs text-muted-foreground">
                                                    <div className="flex flex-wrap gap-2">
                                                        {message.metadata.provider && (
                                                            <Badge variant="outline" className="text-xs">
                                                                Provider: {message.metadata.provider}
                                                            </Badge>
                                                        )}
                                                        {message.metadata.model && (
                                                            <Badge variant="outline" className="text-xs">
                                                                Model: {message.metadata.model}
                                                            </Badge>
                                                        )}
                                                        {message.metadata.responseTime && (
                                                            <Badge variant="outline" className="text-xs">
                                                                <Clock className="h-3 w-3 mr-1" />
                                                                {message.metadata.responseTime}ms
                                                            </Badge>
                                                        )}
                                                        {message.metadata.tokens && (
                                                            <Badge variant="outline" className="text-xs">
                                                                Tokens: {message.metadata.tokens}
                                                            </Badge>
                                                        )}
                                                    </div>
                                                </div>
                                            )}

                                            {message.actions && message.actions.length > 0 && (
                                                <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                                                    <p className="text-xs font-medium mb-1">Actions:</p>
                                                    <div className="flex flex-wrap gap-1">
                                                        {message.actions.map((action, index) => (
                                                            <Badge key={index} variant="secondary" className="text-xs">
                                                                {action.type}
                                                            </Badge>
                                                        ))}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                ))}

                                {isProcessing && (
                                    <div className="flex justify-start">
                                        <div className="bg-secondary text-secondary-foreground rounded-lg p-3">
                                            <div className="flex items-center gap-2">
                                                <RefreshCw className="h-4 w-4 animate-spin" />
                                                <span className="text-sm">Processing with UAUI...</span>
                                            </div>
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Input Area */}
                            <div className="flex gap-2 mt-auto">
                                <Input
                                    placeholder="Type your test message..."
                                    value={inputMessage}
                                    onChange={(e) => setInputMessage(e.target.value)}
                                    onKeyDown={handleKeyDown}
                                    disabled={!isInitialized || isProcessing}
                                    className="flex-1"
                                />
                                <Button
                                    onClick={handleSendMessage}
                                    disabled={!isInitialized || isProcessing || !inputMessage.trim()}
                                >
                                    {isProcessing ? (
                                        <RefreshCw className="h-4 w-4 animate-spin" />
                                    ) : (
                                        <Send className="h-4 w-4" />
                                    )}
                                    <span className="ml-2">Send</span>
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Test Controls */}
                <div className="lg:col-span-4 space-y-4">
                    {/* System Status */}
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">UAUI Status</CardTitle>
                        </CardHeader>
                        <CardContent className="pb-2">
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-sm">System Status:</span>
                                <Badge
                                    variant={isInitialized ? "default" : isLoading ? "secondary" : "outline"}
                                    className="flex items-center gap-1"
                                >
                                    {isInitialized ? (
                                        <><CheckCircle className="h-3 w-3" /> Active</>
                                    ) : isLoading ? (
                                        <><RefreshCw className="h-3 w-3 animate-spin" /> Initializing</>
                                    ) : (
                                        <><AlertCircle className="h-3 w-3" /> Inactive</>
                                    )}
                                </Badge>
                            </div>
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-sm">Available Providers:</span>
                                <span className="font-semibold">
                                    {isInitialized ? availableProviders.length : 0} / {activeProviders.length}
                                </span>
                            </div>
                            <Separator className="my-2" />
                            <div className="mt-2">
                                {!isInitialized ? (
                                    <Button
                                        onClick={initialize}
                                        className="w-full"
                                        disabled={isLoading || activeProviders.length === 0}
                                    >
                                        <Sparkles className="h-4 w-4 mr-2" />
                                        Initialize UAUI
                                    </Button>
                                ) : (
                                    <Button
                                        onClick={reinitialize}
                                        variant="outline"
                                        className="w-full"
                                        disabled={isLoading}
                                    >
                                        <RefreshCw className="h-4 w-4 mr-2" />
                                        Reinitialize
                                    </Button>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Test Templates */}
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Test Templates</CardTitle>
                        </CardHeader>
                        <CardContent className="pb-0">
                            <Tabs defaultValue={testPrompts[0].category}>
                                <TabsList className="w-full">
                                    {testPrompts.map((category) => (
                                        <TabsTrigger
                                            key={category.category}
                                            value={category.category}
                                            className="text-xs"
                                        >
                                            {category.category}
                                        </TabsTrigger>
                                    ))}
                                </TabsList>

                                {testPrompts.map((category) => (
                                    <TabsContent key={category.category} value={category.category} className="mt-2">
                                        <div className="space-y-2">
                                            {category.prompts.map((prompt, idx) => (
                                                <Button
                                                    key={idx}
                                                    variant="outline"
                                                    size="sm"
                                                    className="w-full justify-start text-left h-auto py-2"
                                                    onClick={() => handleUsePrompt(prompt)}
                                                    disabled={!isInitialized || isProcessing}
                                                >
                                                    <span className="truncate">{prompt}</span>
                                                </Button>
                                            ))}
                                        </div>
                                    </TabsContent>
                                ))}
                            </Tabs>
                        </CardContent>
                    </Card>

                    {/* Provider Status */}
                    {isInitialized && (
                        <Card>
                            <CardHeader className="pb-2">
                                <CardTitle className="text-sm font-medium">Active Providers</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    {availableProviders.length > 0 ? (
                                        availableProviders.map((provider, index) => (
                                            <div key={index} className="flex items-center justify-between p-2 bg-muted rounded-md">
                                                <div className="flex items-center gap-2">
                                                    <Bot className="h-4 w-4 text-primary" />
                                                    <span className="text-sm font-medium">{provider}</span>
                                                </div>
                                                <Badge variant="outline" className="text-xs">Active</Badge>
                                            </div>
                                        ))
                                    ) : (
                                        <div className="text-sm text-muted-foreground text-center py-2">
                                            No active providers available
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </FullDashboardLayoutWithUAUI>
    );
} 
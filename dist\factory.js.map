{"version": 3, "file": "factory.js", "sourceRoot": "", "sources": ["../src/factory.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAgOH,4CAGC;AAKD,kDAGC;AAKD,wDAMC;AApPD,iCAAkC;AAElC,mCAAyC;AAEzC,MAAa,WAAW;IACtB;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,UAA8B,EAAE;QAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;QACzC,OAAO,IAAI,eAAQ,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,OAA6B;QAClD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;YAC9B,GAAG,OAAO;YACV,IAAI,EAAE;gBACJ,MAAM,EAAE;oBACN,EAAE,EAAE,QAAQ;oBACZ,IAAI,EAAE,oBAAoB;oBAC1B,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,OAAO;oBAChB,YAAY,EAAE;wBACZ,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;wBAClC,EAAE,IAAI,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,EAAE;qBACzC;oBACD,aAAa,EAAE;wBACb,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,iBAAiB,EAAE;wBACvD,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,gBAAgB,EAAE;qBACtD;oBACD,eAAe,EAAE,IAAI;iBACtB;aACF;SACF,CAAC,CAAC;QAEH,OAAO,IAAI,eAAQ,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,OAAgC;QACxD,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;YAC9B,GAAG,OAAO;YACV,IAAI,EAAE;gBACJ,SAAS,EAAE;oBACT,EAAE,EAAE,WAAW;oBACf,IAAI,EAAE,uBAAuB;oBAC7B,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,OAAO;oBAChB,YAAY,EAAE;wBACZ,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE;wBACvC,EAAE,IAAI,EAAE,eAAe,EAAE,OAAO,EAAE,OAAO,EAAE;qBAC5C;oBACD,aAAa,EAAE;wBACb,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,EAAE,iBAAiB,EAAE;wBACvD,EAAE,SAAS,EAAE,kBAAkB,EAAE,OAAO,EAAE,sBAAsB,EAAE;qBACnE;oBACD,eAAe,EAAE,IAAI;iBACtB;aACF;SACF,CAAC,CAAC;QAEH,OAAO,IAAI,eAAQ,CAAC,MAAM,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,SAAmC;QAC1D,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAChC,EAAE,EAAE,QAAQ,CAAC,EAAE,IAAI,GAAG,QAAQ,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE;YACnD,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE;YAClD,IAAI,EAAE,QAAQ,CAAC,IAAI;YACnB,MAAM,EAAE;gBACN,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC/D,YAAY,EAAE,QAAQ,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBAC9E,SAAS,EAAE,QAAQ,CAAC,SAAS,IAAI,IAAI;gBACrC,WAAW,EAAE,QAAQ,CAAC,WAAW,IAAI,GAAG;gBACxC,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,KAAK;gBAClC,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,CAAC;aAC/B;YACD,YAAY,EAAE,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC;YACzD,MAAM,EAAE;gBACN,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;gBACvB,OAAO,EAAE,CAAC;gBACV,SAAS,EAAE,CAAC;aACb;YACD,OAAO,EAAE;gBACP,aAAa,EAAE,CAAC;gBAChB,WAAW,EAAE,GAAG;gBAChB,cAAc,EAAE,CAAC;gBACjB,WAAW,EAAE,CAAC;gBACd,SAAS,EAAE,CAAC;gBACZ,QAAQ,EAAE,CAAC;aACZ;SACF,CAAC,CAAC,CAAC;IACN,CAAC;IAED,yBAAyB;IAEjB,MAAM,CAAC,WAAW,CAAC,OAA2B;QACpD,OAAO;YACL,IAAI,EAAE;gBACJ,GAAG,sBAAc,CAAC,IAAI;gBACtB,WAAW,EAAG,OAAO,CAAC,WAAwD,IAAI,sBAAc,CAAC,IAAI,CAAC,WAAW;gBACjH,QAAQ,EAAG,OAAO,CAAC,QAAgD,IAAI,sBAAc,CAAC,IAAI,CAAC,QAAQ;aACpG;YACD,EAAE,EAAE;gBACF,GAAG,sBAAc,CAAC,EAAE;gBACpB,SAAS,EAAE,OAAO,CAAC,WAAW,IAAI,EAAE;gBACpC,iBAAiB,EAAG,OAAO,CAAC,iBAA0E,IAAI,sBAAc,CAAC,EAAE,CAAC,iBAAiB;aAC9I;YACD,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,EAAE;YACxB,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,UAAU,EAAE,OAAO,CAAC,UAAU;SAC/B,CAAC;IACJ,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,IAAY;QAC1C,MAAM,QAAQ,GAA6B;YACzC,MAAM,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,eAAe,CAAC;YACjD,MAAM,EAAE,CAAC,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,CAAC;YAC9D,MAAM,EAAE,CAAC,YAAY,EAAE,cAAc,CAAC;YACtC,OAAO,EAAE,CAAC,eAAe,EAAE,gBAAgB,EAAE,eAAe,CAAC;YAC7D,IAAI,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,oBAAoB,CAAC;YACjE,UAAU,EAAE,CAAC,cAAc,EAAE,yBAAyB,EAAE,mBAAmB,EAAE,wBAAwB,CAAC;SACvG,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAC7C,CAAC;IAEO,MAAM,CAAC,uBAAuB,CAAC,IAAY;QACjD,MAAM,aAAa,GAA0B;YAC3C,MAAM,EAAE;gBACN,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;gBACnE,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;gBAClE,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;aACrD;YACD,MAAM,EAAE;gBACN,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,eAAe,EAAE,iBAAiB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;gBAC/E,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;aACjE;YACD,MAAM,EAAE;gBACN,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,YAAY,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;gBACzD,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,mBAAmB,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE;aACnE;YACD,OAAO,EAAE;gBACP,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,eAAe,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;gBAC5D,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;aACpE;YACD,IAAI,EAAE;gBACJ,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,iBAAiB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;gBAC9D,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,oBAAoB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;aACxE;YACD,UAAU,EAAE;gBACV,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,cAAc,EAAE,yBAAyB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;gBACtF,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,mBAAmB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;gBACtE,EAAE,IAAI,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,wBAAwB,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE;aAC5E;SACF,CAAC;QACF,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;CACF;AArKD,kCAqKC;AA8CD,+EAA+E;AAC/E,wBAAwB;AACxB,+EAA+E;AAE/E;;GAEG;AACH,SAAgB,gBAAgB,CAAC,SAAmC;IAClE,MAAM,WAAW,GAAG,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC7D,OAAO,WAAW,CAAC,eAAe,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;AACtD,CAAC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CAAC,SAAmC;IACrE,MAAM,WAAW,GAAG,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC7D,OAAO,WAAW,CAAC,kBAAkB,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC;AACzD,CAAC;AAED;;GAEG;AACH,SAAgB,sBAAsB,CACpC,SAAmC,EACnC,UAA8B,EAAE;IAEhC,MAAM,WAAW,GAAG,WAAW,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;IAC7D,OAAO,WAAW,CAAC,MAAM,CAAC,EAAE,GAAG,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;AACzD,CAAC"}
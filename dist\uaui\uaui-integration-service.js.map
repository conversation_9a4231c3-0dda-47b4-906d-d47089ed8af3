{"version": 3, "file": "uaui-integration-service.js", "sourceRoot": "", "sources": ["../../src/uaui/uaui-integration-service.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;AAEH,0BAMY;AACZ,kEAAgE;AAChE,kEAAyC;AAsBzC,MAAM,sBAAsB;IAK1B,YAAY,SAA0B;QACpC,WAAW,EAAE,aAAa;QAC1B,QAAQ,EAAE,MAAM;QAChB,iBAAiB,EAAE,OAAO;KAC3B;QARO,SAAI,GAAgB,IAAI,CAAC;QACzB,kBAAa,GAAG,KAAK,CAAC;QAQ5B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,2CAA2C;YAC3C,MAAM,EAAE,SAAS,EAAE,GAAG,sCAAkB,CAAC,QAAQ,EAAE,CAAC;YAEpD,yCAAyC;YACzC,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAE5G,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,OAAO,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;gBACrE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,yCAAyC;YACzC,MAAM,aAAa,GAA6B,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAC/E,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,IAAI,EAAE,QAAQ,CAAC,IAAW;gBAC1B,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACjD,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,GAAG;gBAChB,OAAO,EAAE,KAAK;aACf,CAAC,CAAC,CAAC;YAEJ,uBAAuB;YACvB,IAAI,CAAC,IAAI,GAAG,IAAA,0BAAsB,EAAC,aAAa,EAAE;gBAChD,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW;gBACpC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC9B,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB;aACjD,CAAC,CAAC;YAEH,kBAAkB;YAClB,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAE7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,2BAA2B,eAAe,CAAC,MAAM,YAAY,CAAC,CAAC;YAE3E,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,WAAwB;QAC/C,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,MAAM,OAAO,GAAgB;YAC3B,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;YACxB,IAAI,EAAE,YAAY;YAClB,OAAO,EAAE,WAAW,CAAC,OAAO;YAC5B,OAAO,EAAE;gBACP,GAAG,EAAE,cAAc;gBACnB,QAAQ,EAAE,WAAW,CAAC,QAAQ;gBAC9B,MAAM,EAAE,WAAW,CAAC,MAAM;gBAC1B,SAAS,EAAE,WAAW,CAAC,SAAS;gBAChC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB;SACF,CAAC;QAEF,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;YAE3D,gCAAgC;YAChC,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC,QAAQ,CAAC,CAAC;YACpE,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,OAAc,EAAE,QAAgB;QAC3D,MAAM,OAAO,GAAqB;YAChC,OAAO,EAAE,IAAI;YACb,eAAe,EAAE,CAAC;YAClB,MAAM,EAAE,EAAE;SACX,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,CAAC;gBACH,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;oBACpB,KAAK,0BAA0B;wBAC7B,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;wBAC5D,MAAM;oBAER,KAAK,wBAAwB;wBAC3B,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;wBAC1D,MAAM;oBAER,KAAK,oBAAoB;wBACvB,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;wBAC5C,MAAM;oBAER,KAAK,kBAAkB;wBACrB,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;wBACpD,MAAM;oBAER;wBACE,OAAO,CAAC,IAAI,CAAC,wBAAwB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;gBACxD,CAAC;gBAED,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBACjE,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;gBACjD,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB,CAAC,QAAgB,EAAE,UAAe;QACpE,MAAM,oBAAS,CAAC,GAAG,CAAC,YAAY,QAAQ,aAAa,EAAE,UAAU,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAC,oBAAoB,QAAQ,cAAc,EAAE,UAAU,CAAC,CAAC;IACtE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,oBAAoB,CAAC,QAAgB,EAAE,QAAa;QAChE,MAAM,oBAAS,CAAC,GAAG,CAAC,YAAY,QAAQ,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,oBAAoB,QAAQ,YAAY,EAAE,QAAQ,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,UAAe;QAC5D,MAAM,oBAAS,CAAC,GAAG,CAAC,YAAY,QAAQ,cAAc,EAAE,UAAU,CAAC,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,oBAAoB,QAAQ,eAAe,EAAE,UAAU,CAAC,CAAC;IACvE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,gBAAgB,CAAC,OAAY;QACzC,+CAA+C;QAC/C,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,MAAM,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,eAAe,EAAE;gBACpD,MAAM,EAAE,OAAO;aAChB,CAAC,CAAC,CAAC;QACN,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO,EAAE,CAAC;QAC1B,OAAO,MAAM,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC7B,CAAC;QACD,OAAO,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACjB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC7B,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,IAAY;QAClC,MAAM,QAAQ,GAA2B;YACvC,MAAM,EAAE,OAAO;YACf,IAAI,EAAE,iBAAiB;YACvB,UAAU,EAAE,cAAc;YAC1B,MAAM,EAAE,wBAAwB;YAChC,MAAM,EAAE,YAAY;YACpB,OAAO,EAAE,sBAAsB;SAChC,CAAC;QACF,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,eAAe,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AAED,4BAA4B;AAC5B,MAAM,sBAAsB,GAAG,IAAI,sBAAsB,EAAE,CAAC;AAG1D,wDAAsB;AAKxB,kBAAe,sBAAsB,CAAC"}
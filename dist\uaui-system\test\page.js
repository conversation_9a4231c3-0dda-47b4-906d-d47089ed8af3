"use strict";
/**
 * UAUI System Test Page
 * Direct testing interface for UAUI functionality
 */
"use client";
/**
 * UAUI System Test Page
 * Direct testing interface for UAUI functionality
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = UAUISystemTestPage;
const react_1 = __importStar(require("react"));
const EnhancedDashboardLayout_1 = require("@/components/dashboard/EnhancedDashboardLayout");
const uaui_1 = require("../../uaui");
const ai_provider_store_1 = require("@/stores/ai-provider-store");
const button_1 = require("@/components/ui/button");
const input_1 = require("@/components/ui/input");
const select_1 = require("@/components/ui/select");
const card_1 = require("@/components/ui/card");
const badge_1 = require("@/components/ui/badge");
const alert_1 = require("@/components/ui/alert");
const separator_1 = require("@/components/ui/separator");
const tabs_1 = require("@/components/ui/tabs");
const lucide_react_1 = require("lucide-react");
const link_1 = __importDefault(require("next/link"));
function UAUISystemTestPage() {
    const { isInitialized, isLoading, error, availableProviders, initialize, sendMessage, reinitialize } = (0, uaui_1.useUAUI)();
    const { providers } = (0, ai_provider_store_1.useAIProviderStore)();
    const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];
    const [messages, setMessages] = (0, react_1.useState)([
        {
            id: 'welcome',
            role: 'system',
            content: 'Welcome to the UAUI System Testing Interface. Send a message to test AI provider integration.',
            timestamp: Date.now()
        }
    ]);
    const [inputMessage, setInputMessage] = (0, react_1.useState)('');
    const [isProcessing, setIsProcessing] = (0, react_1.useState)(false);
    const [selectedWidgetId, setSelectedWidgetId] = (0, react_1.useState)('test-widget-1');
    const [selectedTest, setSelectedTest] = (0, react_1.useState)('chat');
    const handleSendMessage = async () => {
        if (!inputMessage.trim() || isProcessing || !isInitialized)
            return;
        const userMessage = {
            id: `user-${Date.now()}`,
            role: 'user',
            content: inputMessage.trim(),
            timestamp: Date.now()
        };
        setMessages(prev => [...prev, userMessage]);
        setInputMessage('');
        setIsProcessing(true);
        try {
            // Process with UAUI
            const response = await processChatMessage({
                id: `test-${Date.now()}`,
                message: userMessage.content,
                widgetId: selectedWidgetId,
                userId: 'test-user',
                sessionId: `test-session-${Date.now()}`
            });
            if (response) {
                // Add assistant response
                const assistantMessage = {
                    id: `assistant-${Date.now()}`,
                    role: 'assistant',
                    content: response?.message || '',
                    timestamp: Date.now(),
                    metadata: {
                        provider: response.metadata.provider,
                        model: response.metadata.model,
                        responseTime: response.metadata.responseTime,
                        tokens: response.metadata.tokens
                    },
                    actions: response?.actions
                };
                setMessages(prev => [...prev, assistantMessage]);
                console.log('UAUI Response:', response);
            }
        }
        catch (err) {
            const errorMessage = {
                id: `error-${Date.now()}`,
                role: 'system',
                content: `Error: ${err.message || 'Unknown error occurred'}`,
                timestamp: Date.now()
            };
            setMessages(prev => [...prev, errorMessage]);
            console.error('UAUI Test Error:', err);
        }
        finally {
            setIsProcessing(false);
        }
    };
    // Sample test widget IDs
    const testWidgets = [
        { id: 'test-widget-1', name: 'Test Widget 1' },
        { id: 'test-widget-2', name: 'Test Widget 2' },
        { id: 'demo-widget', name: 'Demo Widget' }
    ];
    // Sample test prompts
    const testPrompts = [
        {
            category: 'General',
            prompts: [
                'Tell me about the UAUI protocol',
                'What kind of AI providers do you support?',
                'Give me a brief introduction to AI assistants'
            ]
        },
        {
            category: 'Widget Actions',
            prompts: [
                'Change the widget color to blue',
                'Make the widget more professional looking',
                'Update the widget to have a friendly tone'
            ]
        },
        {
            category: 'Performance Tests',
            prompts: [
                'I need a really quick response',
                'Can you analyze this complex data set?',
                'Compare the performance of different AI models'
            ]
        }
    ];
    const handleUsePrompt = (prompt) => {
        setInputMessage(prompt);
    };
    const handleClearConversation = () => {
        setMessages([
            {
                id: 'welcome',
                role: 'system',
                content: 'Welcome to the UAUI System Testing Interface. Send a message to test AI provider integration.',
                timestamp: Date.now()
            }
        ]);
    };
    // Handle Enter key press
    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    };
    return (<EnhancedDashboardLayout_1.FullDashboardLayoutWithUAUI pageTitle="UAUI System Test" pageDescription="Direct testing interface for UAUI functionality" headerActions={<div className="flex items-center gap-2">
                    <link_1.default href="/dashboard/uaui-system">
                        <button_1.Button variant="outline" size="sm">
                            <lucide_react_1.ArrowLeft className="h-4 w-4 mr-2"/>
                            Back to UAUI Dashboard
                        </button_1.Button>
                    </link_1.default>
                    <button_1.Button onClick={reinitialize} size="sm" disabled={isLoading}>
                        <lucide_react_1.RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`}/>
                        Reinitialize
                    </button_1.Button>
                </div>}>
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
                {/* Test Interface */}
                <div className="lg:col-span-8 space-y-4">
                    <card_1.Card className="h-[600px] flex flex-col">
                        <card_1.CardHeader className="pb-3 flex flex-row justify-between items-center">
                            <card_1.CardTitle className="text-lg font-semibold flex items-center">
                                <lucide_react_1.Brain className="h-5 w-5 text-primary mr-2"/>
                                UAUI Test Console
                                {isInitialized && (<badge_1.Badge variant="outline" className="ml-2">
                                        {availableProviders.length} Providers
                                    </badge_1.Badge>)}
                            </card_1.CardTitle>
                            <div className="flex items-center gap-2">
                                <select_1.Select value={selectedWidgetId} onValueChange={setSelectedWidgetId}>
                                    <select_1.SelectTrigger className="w-[180px]">
                                        <select_1.SelectValue placeholder="Select Widget"/>
                                    </select_1.SelectTrigger>
                                    <select_1.SelectContent>
                                        {testWidgets.map(widget => (<select_1.SelectItem key={widget.id} value={widget.id}>
                                                {widget.name}
                                            </select_1.SelectItem>))}
                                    </select_1.SelectContent>
                                </select_1.Select>
                                <button_1.Button variant="ghost" size="sm" onClick={handleClearConversation}>
                                    Clear
                                </button_1.Button>
                            </div>
                        </card_1.CardHeader>
                        <card_1.CardContent className="flex-1 overflow-hidden flex flex-col">
                            {/* Status Alert */}
                            {!isInitialized && !isLoading && (<alert_1.Alert className="mb-3">
                                    <lucide_react_1.Sparkles className="h-4 w-4"/>
                                    <alert_1.AlertDescription>
                                        UAUI is not initialized. Click the Initialize button to start.
                                    </alert_1.AlertDescription>
                                </alert_1.Alert>)}

                            {error && (<alert_1.Alert variant="destructive" className="mb-3">
                                    <lucide_react_1.AlertCircle className="h-4 w-4"/>
                                    <alert_1.AlertDescription>
                                        {error}
                                    </alert_1.AlertDescription>
                                </alert_1.Alert>)}

                            {/* Messages Container */}
                            <div className="flex-1 overflow-y-auto mb-4 p-1 space-y-4">
                                {messages.map((message) => (<div key={message.id} className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                                        <div className={`max-w-[85%] rounded-lg p-3 ${message.role === 'user'
                ? 'bg-primary text-primary-foreground'
                : message.role === 'system'
                    ? 'bg-muted text-muted-foreground'
                    : 'bg-secondary text-secondary-foreground'}`}>
                                            <p className="text-sm whitespace-pre-wrap">{message.content}</p>

                                            {message.metadata && (<div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700 text-xs text-muted-foreground">
                                                    <div className="flex flex-wrap gap-2">
                                                        {message.metadata.provider && (<badge_1.Badge variant="outline" className="text-xs">
                                                                Provider: {message.metadata.provider}
                                                            </badge_1.Badge>)}
                                                        {message.metadata.model && (<badge_1.Badge variant="outline" className="text-xs">
                                                                Model: {message.metadata.model}
                                                            </badge_1.Badge>)}
                                                        {message.metadata.responseTime && (<badge_1.Badge variant="outline" className="text-xs">
                                                                <lucide_react_1.Clock className="h-3 w-3 mr-1"/>
                                                                {message.metadata.responseTime}ms
                                                            </badge_1.Badge>)}
                                                        {message.metadata.tokens && (<badge_1.Badge variant="outline" className="text-xs">
                                                                Tokens: {message.metadata.tokens}
                                                            </badge_1.Badge>)}
                                                    </div>
                                                </div>)}

                                            {message.actions && message.actions.length > 0 && (<div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-700">
                                                    <p className="text-xs font-medium mb-1">Actions:</p>
                                                    <div className="flex flex-wrap gap-1">
                                                        {message.actions.map((action, index) => (<badge_1.Badge key={index} variant="secondary" className="text-xs">
                                                                {action.type}
                                                            </badge_1.Badge>))}
                                                    </div>
                                                </div>)}
                                        </div>
                                    </div>))}

                                {isProcessing && (<div className="flex justify-start">
                                        <div className="bg-secondary text-secondary-foreground rounded-lg p-3">
                                            <div className="flex items-center gap-2">
                                                <lucide_react_1.RefreshCw className="h-4 w-4 animate-spin"/>
                                                <span className="text-sm">Processing with UAUI...</span>
                                            </div>
                                        </div>
                                    </div>)}
                            </div>

                            {/* Input Area */}
                            <div className="flex gap-2 mt-auto">
                                <input_1.Input placeholder="Type your test message..." value={inputMessage} onChange={(e) => setInputMessage(e.target.value)} onKeyDown={handleKeyDown} disabled={!isInitialized || isProcessing} className="flex-1"/>
                                <button_1.Button onClick={handleSendMessage} disabled={!isInitialized || isProcessing || !inputMessage.trim()}>
                                    {isProcessing ? (<lucide_react_1.RefreshCw className="h-4 w-4 animate-spin"/>) : (<lucide_react_1.Send className="h-4 w-4"/>)}
                                    <span className="ml-2">Send</span>
                                </button_1.Button>
                            </div>
                        </card_1.CardContent>
                    </card_1.Card>
                </div>

                {/* Test Controls */}
                <div className="lg:col-span-4 space-y-4">
                    {/* System Status */}
                    <card_1.Card>
                        <card_1.CardHeader className="pb-2">
                            <card_1.CardTitle className="text-sm font-medium">UAUI Status</card_1.CardTitle>
                        </card_1.CardHeader>
                        <card_1.CardContent className="pb-2">
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-sm">System Status:</span>
                                <badge_1.Badge variant={isInitialized ? "default" : isLoading ? "secondary" : "outline"} className="flex items-center gap-1">
                                    {isInitialized ? (<><lucide_react_1.CheckCircle className="h-3 w-3"/> Active</>) : isLoading ? (<><lucide_react_1.RefreshCw className="h-3 w-3 animate-spin"/> Initializing</>) : (<><lucide_react_1.AlertCircle className="h-3 w-3"/> Inactive</>)}
                                </badge_1.Badge>
                            </div>
                            <div className="flex items-center justify-between mb-2">
                                <span className="text-sm">Available Providers:</span>
                                <span className="font-semibold">
                                    {isInitialized ? availableProviders.length : 0} / {activeProviders.length}
                                </span>
                            </div>
                            <separator_1.Separator className="my-2"/>
                            <div className="mt-2">
                                {!isInitialized ? (<button_1.Button onClick={initialize} className="w-full" disabled={isLoading || activeProviders.length === 0}>
                                        <lucide_react_1.Sparkles className="h-4 w-4 mr-2"/>
                                        Initialize UAUI
                                    </button_1.Button>) : (<button_1.Button onClick={reinitialize} variant="outline" className="w-full" disabled={isLoading}>
                                        <lucide_react_1.RefreshCw className="h-4 w-4 mr-2"/>
                                        Reinitialize
                                    </button_1.Button>)}
                            </div>
                        </card_1.CardContent>
                    </card_1.Card>

                    {/* Test Templates */}
                    <card_1.Card>
                        <card_1.CardHeader className="pb-2">
                            <card_1.CardTitle className="text-sm font-medium">Test Templates</card_1.CardTitle>
                        </card_1.CardHeader>
                        <card_1.CardContent className="pb-0">
                            <tabs_1.Tabs defaultValue={testPrompts[0].category}>
                                <tabs_1.TabsList className="w-full">
                                    {testPrompts.map((category) => (<tabs_1.TabsTrigger key={category.category} value={category.category} className="text-xs">
                                            {category.category}
                                        </tabs_1.TabsTrigger>))}
                                </tabs_1.TabsList>

                                {testPrompts.map((category) => (<tabs_1.TabsContent key={category.category} value={category.category} className="mt-2">
                                        <div className="space-y-2">
                                            {category.prompts.map((prompt, idx) => (<button_1.Button key={idx} variant="outline" size="sm" className="w-full justify-start text-left h-auto py-2" onClick={() => handleUsePrompt(prompt)} disabled={!isInitialized || isProcessing}>
                                                    <span className="truncate">{prompt}</span>
                                                </button_1.Button>))}
                                        </div>
                                    </tabs_1.TabsContent>))}
                            </tabs_1.Tabs>
                        </card_1.CardContent>
                    </card_1.Card>

                    {/* Provider Status */}
                    {isInitialized && (<card_1.Card>
                            <card_1.CardHeader className="pb-2">
                                <card_1.CardTitle className="text-sm font-medium">Active Providers</card_1.CardTitle>
                            </card_1.CardHeader>
                            <card_1.CardContent>
                                <div className="space-y-2">
                                    {availableProviders.length > 0 ? (availableProviders.map((provider, index) => (<div key={index} className="flex items-center justify-between p-2 bg-muted rounded-md">
                                                <div className="flex items-center gap-2">
                                                    <lucide_react_1.Bot className="h-4 w-4 text-primary"/>
                                                    <span className="text-sm font-medium">{provider}</span>
                                                </div>
                                                <badge_1.Badge variant="outline" className="text-xs">Active</badge_1.Badge>
                                            </div>))) : (<div className="text-sm text-muted-foreground text-center py-2">
                                            No active providers available
                                        </div>)}
                                </div>
                            </card_1.CardContent>
                        </card_1.Card>)}
                </div>
            </div>
        </EnhancedDashboardLayout_1.FullDashboardLayoutWithUAUI>);
}
//# sourceMappingURL=page.js.map
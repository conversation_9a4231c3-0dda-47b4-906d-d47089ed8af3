"use strict";
/**
 * Universal AI-UI Protocol (UAUI) - Core Engine
 * Production-ready, independent protocol implementation
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UAUIApp = exports.UAUICore = void 0;
const events_1 = require("events");
const event_bus_1 = require("./event-bus");
const state_manager_1 = require("./state-manager");
const app_router_1 = require("./app-router");
const ai_service_1 = require("./ai-service");
const logger_1 = require("./utils/logger");
class UAUICore extends events_1.EventEmitter {
    constructor(config) {
        super();
        this.apps = new Map();
        this.isInitialized = false;
        this.config = config;
        this.logger = new logger_1.Logger({ level: config.core.logLevel });
        this.eventBus = new event_bus_1.EventBus(this.logger);
        this.stateManager = new state_manager_1.StateManager(this.logger);
        this.appRouter = new app_router_1.AppRouter(this.logger);
        this.aiService = new ai_service_1.UniversalAIService(config.ai, this.logger);
    }
    /**
     * Initialize the UAUI core engine
     */
    async initialize() {
        if (this.isInitialized) {
            throw new Error('UAUI Core already initialized');
        }
        try {
            this.logger.info('Initializing UAUI Core Engine...');
            // Initialize components
            await this.eventBus.initialize();
            await this.stateManager.initialize();
            await this.appRouter.initialize();
            await this.aiService.initialize();
            // Setup event handlers
            this.setupEventHandlers();
            // Register configured apps
            for (const [appId, appConfig] of Object.entries(this.config.apps)) {
                await this.registerApp(appId, appConfig);
            }
            this.isInitialized = true;
            this.logger.info('UAUI Core Engine initialized successfully');
            this.emit('initialized');
        }
        catch (error) {
            this.logger.error('Failed to initialize UAUI Core:', error);
            throw error;
        }
    }
    /**
     * Register a new application with the UAUI protocol
     */
    async registerApp(appId, config) {
        if (this.apps.has(appId)) {
            throw new Error(`App ${appId} already registered`);
        }
        const app = new UAUIApp(appId, config, this);
        await app.initialize();
        this.apps.set(appId, app);
        this.appRouter.registerApp(appId, app);
        this.logger.info(`Registered app: ${appId}`);
        this.emit('app_registered', { appId, config });
        return app;
    }
    /**
     * Process an AI request through the UAUI protocol
     */
    async processAIRequest(request) {
        const startTime = Date.now();
        try {
            this.logger.debug('Processing AI request:', request.id);
            // Validate request
            this.validateRequest(request);
            // Enrich context
            const enrichedContext = await this.enrichContext(request.context);
            // Process with AI service
            const aiResponse = await this.aiService.process(request, enrichedContext);
            // Plan and execute actions
            const actions = await this.planActions(aiResponse, enrichedContext);
            const crossAppAction = actions.find(a => a.type.startsWith('cross_app.'));
            // Execute cross-app actions if any
            if (crossAppAction) {
                await this.executeCrossAppAction({
                    targetApp: crossAppAction.target,
                    action: crossAppAction,
                    context: enrichedContext
                });
            }
            // Build response
            const response = {
                id: `response-${Date.now()}`,
                requestId: request.id,
                message: aiResponse.message,
                actions,
                crossAppAction: crossAppAction ? {
                    targetApp: crossAppAction.target,
                    action: crossAppAction,
                    context: enrichedContext
                } : undefined,
                data: aiResponse.data,
                metadata: {
                    ...aiResponse.metadata,
                    responseTime: Date.now() - startTime,
                    timestamp: Date.now()
                }
            };
            // Emit response event
            this.eventBus.emit({
                id: `event-${Date.now()}`,
                type: 'ai.response',
                timestamp: Date.now(),
                source: { app: 'uaui-core' },
                target: { app: request.context.app },
                data: response
            });
            this.logger.debug('AI request processed successfully:', request.id);
            return response;
        }
        catch (error) {
            this.logger.error('Failed to process AI request:', error);
            const errorResponse = {
                id: `error-${Date.now()}`,
                requestId: request.id,
                metadata: {
                    provider: 'error',
                    model: 'error',
                    responseTime: Date.now() - startTime,
                    timestamp: Date.now()
                },
                error: {
                    code: 'INTERNAL_ERROR',
                    message: error instanceof Error ? error.message : 'Unknown error',
                    timestamp: Date.now(),
                    recoverable: true
                }
            };
            return errorResponse;
        }
    }
    /**
     * Synchronize state between applications
     */
    async syncState(fromApp, toApp, state) {
        try {
            await this.stateManager.sync(fromApp, toApp, state);
            this.eventBus.emit({
                id: `sync-${Date.now()}`,
                type: 'state.sync',
                timestamp: Date.now(),
                source: { app: fromApp },
                target: { app: toApp },
                data: state,
                metadata: { crossApp: true }
            });
            this.logger.debug(`State synced from ${fromApp} to ${toApp}`);
        }
        catch (error) {
            this.logger.error('Failed to sync state:', error);
            throw error;
        }
    }
    /**
     * Execute a cross-application action
     */
    async executeCrossAppAction(crossAppAction) {
        try {
            const targetApp = this.apps.get(crossAppAction.targetApp);
            if (!targetApp) {
                throw new Error(`Target app not found: ${crossAppAction.targetApp}`);
            }
            await this.appRouter.routeAction(crossAppAction);
            this.eventBus.emit({
                id: `cross-app-${Date.now()}`,
                type: 'cross_app.action',
                timestamp: Date.now(),
                source: { app: 'uaui-core' },
                target: { app: crossAppAction.targetApp },
                data: crossAppAction,
                metadata: { crossApp: true }
            });
            this.logger.debug(`Cross-app action executed: ${crossAppAction.targetApp}`);
        }
        catch (error) {
            this.logger.error('Failed to execute cross-app action:', error);
            throw error;
        }
    }
    /**
     * Get application instance
     */
    getApp(appId) {
        return this.apps.get(appId);
    }
    /**
     * Get all registered applications
     */
    getApps() {
        return new Map(this.apps);
    }
    /**
     * Get UAUI configuration
     */
    getConfig() {
        return { ...this.config };
    }
    /**
     * Get AI service instance
     */
    getAIService() {
        return this.aiService;
    }
    /**
     * Get event bus instance
     */
    getEventBus() {
        return this.eventBus;
    }
    /**
     * Shutdown the UAUI core engine
     */
    async shutdown() {
        try {
            this.logger.info('Shutting down UAUI Core Engine...');
            // Shutdown all apps
            for (const app of this.apps.values()) {
                await app.shutdown();
            }
            // Shutdown components
            await this.aiService.shutdown();
            await this.appRouter.shutdown();
            await this.stateManager.shutdown();
            await this.eventBus.shutdown();
            this.isInitialized = false;
            this.logger.info('UAUI Core Engine shut down successfully');
            this.emit('shutdown');
        }
        catch (error) {
            this.logger.error('Error during shutdown:', error);
            throw error;
        }
    }
    // Private methods
    setupEventHandlers() {
        this.eventBus.on('ai.request', this.handleAIRequest.bind(this));
        this.eventBus.on('cross_app.action', this.handleCrossAppAction.bind(this));
        this.eventBus.on('state.sync', this.handleStateSync.bind(this));
    }
    async handleAIRequest(event) {
        const request = event.data;
        await this.processAIRequest(request);
    }
    async handleCrossAppAction(event) {
        const action = event.data;
        await this.executeCrossAppAction(action);
    }
    async handleStateSync(event) {
        const { fromApp, toApp, state } = event.data;
        await this.syncState(fromApp, toApp, state);
    }
    validateRequest(request) {
        if (!request.id || !request.type || !request.context) {
            throw new Error('Invalid UAUI request: missing required fields');
        }
    }
    async enrichContext(context) {
        // Add cross-app context, user history, preferences, etc.
        const enriched = { ...context };
        // Add session information
        if (!enriched.session) {
            enriched.session = {
                id: `session-${Date.now()}`,
                startTime: Date.now(),
                lastActivity: Date.now(),
                interactions: 0
            };
        }
        // Add system context
        enriched.system = {
            timestamp: Date.now(),
            version: this.config.core.version,
            environment: this.config.core.environment
        };
        return enriched;
    }
    async planActions(aiResponse, context) {
        // This would contain sophisticated action planning logic
        // For now, return actions from AI response
        return aiResponse.actions || [];
    }
}
exports.UAUICore = UAUICore;
/**
 * UAUI Application wrapper
 */
class UAUIApp {
    constructor(id, config, core) {
        this.isInitialized = false;
        this.id = id;
        this.config = config;
        this.core = core;
    }
    async initialize() {
        if (this.isInitialized)
            return;
        // Setup app-specific event handlers
        for (const handler of this.config.eventHandlers) {
            this.core.getEventBus().on(handler.eventType, this.handleEvent.bind(this));
        }
        this.isInitialized = true;
    }
    async shutdown() {
        this.isInitialized = false;
    }
    getId() {
        return this.id;
    }
    getConfig() {
        return { ...this.config };
    }
    async handleEvent(event) {
        // Handle app-specific events
        console.log(`App ${this.id} handling event:`, event.type);
    }
}
exports.UAUIApp = UAUIApp;
//# sourceMappingURL=core.js.map
{"version": 3, "file": "use-uaui.js", "sourceRoot": "", "sources": ["../../../src/uaui/hooks/use-uaui.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;AA6CH,0BA0LC;AArOD,iCAAiE;AACjE,kEAAgE;AAChE,oGAG8C;AAmC9C;;GAEG;AACH,SAAgB,OAAO,CAAC,UAA0B,EAAE;IAClD,MAAM,EACJ,cAAc,GAAG,IAAI,EACrB,WAAW,GAAG,aAAa,EAC3B,QAAQ,GAAG,MAAM,EAClB,GAAG,OAAO,CAAC;IAEZ,QAAQ;IACR,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAC1D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAClD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,IAAA,gBAAQ,EAAgB,IAAI,CAAC,CAAC;IACxD,MAAM,CAAC,kBAAkB,EAAE,qBAAqB,CAAC,GAAG,IAAA,gBAAQ,EAAW,EAAE,CAAC,CAAC;IAE3E,OAAO;IACP,MAAM,uBAAuB,GAAG,IAAA,cAAM,EAAC,KAAK,CAAC,CAAC;IAE9C,oBAAoB;IACpB,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,IAAA,sCAAkB,GAAE,CAAC;IAE3D;;OAEG;IACH,MAAM,UAAU,GAAG,IAAA,mBAAW,EAAC,KAAK,IAAsB,EAAE;QAC1D,IAAI,SAAS;YAAE,OAAO,KAAK,CAAC;QAE5B,YAAY,CAAC,IAAI,CAAC,CAAC;QACnB,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEf,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;YAE9C,MAAM,OAAO,GAAG,MAAM,kCAAsB,CAAC,UAAU,EAAE,CAAC;YAE1D,IAAI,OAAO,EAAE,CAAC;gBACZ,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBAEvB,0BAA0B;gBAC1B,MAAM,SAAS,GAAG,kCAAsB,CAAC,qBAAqB,EAAE,CAAC;gBACjE,qBAAqB,CAAC,SAAS,CAAC,CAAC;gBAEjC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;gBAC/C,OAAO,CAAC,GAAG,CAAC,2BAA2B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,kCAAkC,CAAC,CAAC;gBAC7C,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAChD,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACnF,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC;YACnD,OAAO,KAAK,CAAC;QACf,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;IAEhB;;OAEG;IACH,MAAM,YAAY,GAAG,IAAA,mBAAW,EAAC,KAAK,IAAsB,EAAE;QAC5D,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAEhD,IAAI,CAAC;YACH,MAAM,QAAQ,EAAE,CAAC;YACjB,OAAO,MAAM,UAAU,EAAE,CAAC;QAC5B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,yBAAyB,CAAC;YACpF,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC;YACrD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;IAEjB;;OAEG;IACH,MAAM,QAAQ,GAAG,IAAA,mBAAW,EAAC,KAAK,IAAmB,EAAE;QACrD,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAE/C,IAAI,CAAC;YACH,MAAM,kCAAsB,CAAC,QAAQ,EAAE,CAAC;YACxC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACxB,qBAAqB,CAAC,EAAE,CAAC,CAAC;YAC1B,QAAQ,CAAC,IAAI,CAAC,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP;;OAEG;IACH,MAAM,UAAU,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE;QAClC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP;;OAEG;IACH,MAAM,WAAW,GAAG,IAAA,mBAAW,EAAC,KAAK,EAAE,OAAoB,EAAgC,EAAE;QAC3F,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,kCAAsB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACnE,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,MAAM,YAAY,GAAG,GAAG,YAAY,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,CAAC;YACnF,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;IAEpB;;OAEG;IACH,MAAM,OAAO,GAAG,IAAA,mBAAW,EAAC,GAAY,EAAE;QACxC,OAAO,kCAAsB,CAAC,OAAO,EAAE,CAAC;IAC1C,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP;;OAEG;IACH,MAAM,SAAS,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE;QACjC,OAAO;YACL,WAAW,EAAE,aAAa;YAC1B,OAAO,EAAE,SAAS;YAClB,KAAK;YACL,SAAS,EAAE,kBAAkB;SAC9B,CAAC;IACJ,CAAC,EAAE,CAAC,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC,CAAC;IAE1D,2BAA2B;IAC3B,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,IAAI,cAAc,IAAI,CAAC,uBAAuB,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3G,uBAAuB,CAAC,OAAO,GAAG,IAAI,CAAC;YACvC,UAAU,EAAE,CAAC;QACf,CAAC;IACH,CAAC,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;IAE5C,8BAA8B;IAC9B,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,IAAI,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtE,qCAAqC;YACrC,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5G,IAAI,eAAe,CAAC,MAAM,KAAK,kBAAkB,CAAC,MAAM,EAAE,CAAC;gBACzD,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;gBAC/D,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;IACH,CAAC,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,kBAAkB,EAAE,YAAY,CAAC,CAAC,CAAC;IAEjE,qBAAqB;IACrB,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,OAAO,GAAG,EAAE;YACV,IAAI,aAAa,EAAE,CAAC;gBAClB,QAAQ,EAAE,CAAC;YACb,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE9B,OAAO;QACL,QAAQ;QACR,aAAa;QACb,SAAS;QACT,KAAK;QACL,kBAAkB;QAElB,UAAU;QACV,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,UAAU;QAEV,YAAY;QACZ,WAAW;QAEX,YAAY;QACZ,OAAO;QACP,SAAS;KACV,CAAC;AACJ,CAAC"}
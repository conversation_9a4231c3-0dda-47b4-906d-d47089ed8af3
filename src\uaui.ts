/**
 * UAUI Protocol - Standalone Implementation
 * Universal AI User Interface Protocol
 */

import {
  UAUIConfig,
  UAUIRequest,
  UAUIResponse,
  AIProvider,
  AIProviderType,
  RequestContext,
  ExistingProviderConfig
} from './types';

// ============================================================================
// LOGGER
// ============================================================================

class Logger {
  constructor(private level: string = 'info') { }

  debug(msg: string, data?: any) { if (this.level === 'debug') console.log(`[DEBUG] ${msg}`, data || ''); }
  info(msg: string, data?: any) { console.log(`[INFO] ${msg}`, data || ''); }
  warn(msg: string, data?: any) { console.log(`[WARN] ${msg}`, data || ''); }
  error(msg: string, data?: any) { console.log(`[ERROR] ${msg}`, data || ''); }
}

// ============================================================================
// AI PROVIDER ADAPTERS
// ============================================================================

abstract class AIProviderAdapter {
  constructor(protected provider: AIProvider, protected logger: Logger) { }

  abstract process(request: UAUIRequest, context: RequestContext): Promise<any>;
  abstract test(): Promise<boolean>;

  getId(): string { return this.provider.id; }
  getName(): string { return this.provider.name; }
  getType(): AIProviderType { return this.provider.type; }

  protected generateActions(responseText: string, request: UAUIRequest): any[] {
    const actions: any[] = [];
    const lowerText = responseText.toLowerCase();
    const lowerMessage = (request.message || '').toLowerCase();

    // Color change actions
    if (lowerText.includes('color') || lowerMessage.includes('color')) {
      if (lowerText.includes('blue') || lowerMessage.includes('blue')) {
        actions.push({
          id: `action-${Date.now()}`,
          type: 'widget.appearance.update',
          target: 'widget',
          payload: { primaryColor: '#3b82f6' }
        });
      } else if (lowerText.includes('red') || lowerMessage.includes('red')) {
        actions.push({
          id: `action-${Date.now()}`,
          type: 'widget.appearance.update',
          target: 'widget',
          payload: { primaryColor: '#ef4444' }
        });
      }
    }

    // Navigation actions
    if (lowerText.includes('analytics') || lowerText.includes('dashboard') ||
      lowerMessage.includes('analytics') || lowerMessage.includes('dashboard')) {
      actions.push({
        id: `action-${Date.now()}`,
        type: 'cross_app.navigate',
        target: 'dashboard',
        payload: { view: 'analytics', timeRange: 'last_30_days' }
      });
    }

    return actions;
  }
}

// OpenAI Adapter
class OpenAIAdapter extends AIProviderAdapter {
  async process(request: UAUIRequest, context: RequestContext): Promise<any> {
    const apiKey = this.provider.config.apiKey;
    if (!apiKey) throw new Error('OpenAI API key not configured');

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), this.provider.config.timeout || 30000);

    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.provider.config.defaultModel || 'gpt-4',
          messages: [
            { role: 'system', content: 'You are a helpful AI assistant integrated into a widget system.' },
            { role: 'user', content: request.message || '' }
          ],
          max_tokens: this.provider.config.maxTokens || 4000,
          temperature: this.provider.config.temperature || 0.7
        }),
        signal: controller.signal
      });

      clearTimeout(timeout);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})) as any;
        throw new Error(`OpenAI API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json() as any;
      const responseText = data.choices?.[0]?.message?.content || 'No response received';

      return {
        message: responseText,
        tokens: data.usage?.total_tokens || 0,
        actions: this.generateActions(responseText, request)
      };
    } catch (error: any) {
      clearTimeout(timeout);
      if (error.name === 'AbortError') throw new Error('OpenAI request timeout');
      throw error;
    }
  }

  async test(): Promise<boolean> {
    try {
      const apiKey = this.provider.config.apiKey;
      if (!apiKey) return false;
      const response = await fetch('https://api.openai.com/v1/models', {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      });
      return response.ok;
    } catch { return false; }
  }
}

// Groq Adapter
class GroqAdapter extends AIProviderAdapter {
  async process(request: UAUIRequest, context: RequestContext): Promise<any> {
    const apiKey = this.provider.config.apiKey;
    if (!apiKey) throw new Error('Groq API key not configured');

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), this.provider.config.timeout || 15000);

    try {
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          model: this.provider.config.defaultModel || 'llama3-70b-8192',
          messages: [
            { role: 'system', content: 'You are a helpful AI assistant. Provide quick, clear responses.' },
            { role: 'user', content: request.message || '' }
          ],
          max_tokens: this.provider.config.maxTokens || 4000,
          temperature: this.provider.config.temperature || 0.7
        }),
        signal: controller.signal
      });

      clearTimeout(timeout);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})) as any;
        throw new Error(`Groq API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json() as any;
      const responseText = data.choices?.[0]?.message?.content || 'No response received';

      return {
        message: responseText,
        tokens: data.usage?.total_tokens || 0,
        actions: this.generateActions(responseText, request)
      };
    } catch (error: any) {
      clearTimeout(timeout);
      if (error.name === 'AbortError') throw new Error('Groq request timeout');
      throw error;
    }
  }

  async test(): Promise<boolean> {
    try {
      const apiKey = this.provider.config.apiKey;
      if (!apiKey) return false;
      const response = await fetch('https://api.groq.com/openai/v1/models', {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      });
      return response.ok;
    } catch { return false; }
  }
}

// OpenRouter Adapter
class OpenRouterAdapter extends AIProviderAdapter {
  async process(request: UAUIRequest, context: RequestContext): Promise<any> {
    const apiKey = this.provider.config.apiKey;
    if (!apiKey) throw new Error('OpenRouter API key not configured');

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), this.provider.config.timeout || 30000);

    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://uaui-protocol.dev',
          'X-Title': 'UAUI Protocol'
        },
        body: JSON.stringify({
          model: this.provider.config.defaultModel || 'openai/gpt-4',
          messages: [
            { role: 'system', content: 'You are a helpful AI assistant with access to multiple models.' },
            { role: 'user', content: request.message || '' }
          ],
          max_tokens: this.provider.config.maxTokens || 4000,
          temperature: this.provider.config.temperature || 0.7
        }),
        signal: controller.signal
      });

      clearTimeout(timeout);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})) as any;
        throw new Error(`OpenRouter API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json() as any;
      const responseText = data.choices?.[0]?.message?.content || 'No response received';

      return {
        message: responseText,
        tokens: data.usage?.total_tokens || 0,
        actions: this.generateActions(responseText, request)
      };
    } catch (error: any) {
      clearTimeout(timeout);
      if (error.name === 'AbortError') throw new Error('OpenRouter request timeout');
      throw error;
    }
  }

  async test(): Promise<boolean> {
    try {
      const apiKey = this.provider.config.apiKey;
      if (!apiKey) return false;
      const response = await fetch('https://openrouter.ai/api/v1/models', {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      });
      return response.ok;
    } catch { return false; }
  }
}

// ============================================================================
// SMART PROVIDER SELECTION
// ============================================================================

class SmartSelector {
  constructor(private logger: Logger) { }

  select(request: UAUIRequest, providers: Map<string, AIProviderAdapter>): AIProviderAdapter {
    const message = (request.message || '').toLowerCase();

    // Smart selection logic
    if (message.includes('quick') || message.includes('fast') || message.includes('now')) {
      return this.findProvider(providers, 'groq') || this.getFirstProvider(providers);
    }

    if (message.includes('compare') || message.includes('multiple') || message.includes('models')) {
      return this.findProvider(providers, 'openrouter') || this.getFirstProvider(providers);
    }

    if (message.includes('analyze') || message.includes('technical') || message.includes('code')) {
      return this.findProvider(providers, 'openai') || this.getFirstProvider(providers);
    }

    // Default to OpenAI
    return this.findProvider(providers, 'openai') || this.getFirstProvider(providers);
  }

  private findProvider(providers: Map<string, AIProviderAdapter>, type: string): AIProviderAdapter | null {
    for (const provider of providers.values()) {
      if (provider.getType() === type) return provider;
    }
    return null;
  }

  private getFirstProvider(providers: Map<string, AIProviderAdapter>): AIProviderAdapter {
    const provider = providers.values().next().value;
    if (!provider) throw new Error('No AI providers available');
    return provider;
  }
}

// ============================================================================
// MAIN UAUI CLASS
// ============================================================================

export class UAUI {
  private config: UAUIConfig;
  private logger: Logger;
  private providers = new Map<string, AIProviderAdapter>();
  private selector: SmartSelector;
  private isInitialized = false;

  constructor(config: UAUIConfig) {
    this.config = config;
    this.logger = new Logger(config.core.logLevel);
    this.selector = new SmartSelector(this.logger);
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) throw new Error('UAUI already initialized');

    this.logger.info('Initializing UAUI Protocol...');

    // Initialize providers
    for (const providerConfig of this.config.ai.providers) {
      const adapter = this.createAdapter(providerConfig);
      await adapter.test(); // Validate API key
      this.providers.set(providerConfig.id, adapter);
      this.logger.info(`Initialized provider: ${providerConfig.name}`);
    }

    this.isInitialized = true;
    this.logger.info('UAUI Protocol initialized successfully');
  }

  async processAIRequest(request: UAUIRequest): Promise<UAUIResponse> {
    if (!this.isInitialized) throw new Error('UAUI not initialized');

    const startTime = Date.now();

    try {
      // Smart provider selection
      const provider = this.selector.select(request, this.providers);
      this.logger.debug(`Selected provider: ${provider.getName()}`);

      // Process request
      const result = await provider.process(request, request.context || {});

      return {
        id: `response-${Date.now()}`,
        requestId: request.id,
        message: result.message,
        actions: result.actions || [],
        data: result.data,
        metadata: {
          provider: provider.getName(),
          model: this.config.ai.providers.find(p => p.id === provider.getId())?.config.defaultModel || 'unknown',
          tokens: result.tokens,
          responseTime: Date.now() - startTime,
          timestamp: Date.now()
        }
      };
    } catch (error: any) {
      this.logger.error('AI request failed:', error);

      return {
        id: `error-${Date.now()}`,
        requestId: request.id,
        message: 'Sorry, I encountered an error processing your request.',
        metadata: {
          provider: 'error',
          model: 'error',
          responseTime: Date.now() - startTime,
          timestamp: Date.now()
        },
        error: {
          code: 'PROCESSING_ERROR',
          message: error.message,
          timestamp: Date.now()
        }
      };
    }
  }

  async getAvailableProviders(): Promise<string[]> {
    return Array.from(this.providers.keys());
  }

  async shutdown(): Promise<void> {
    this.providers.clear();
    this.isInitialized = false;
    this.logger.info('UAUI Protocol shut down');
  }

  private createAdapter(provider: AIProvider): AIProviderAdapter {
    switch (provider.type) {
      case 'openai': return new OpenAIAdapter(provider, this.logger);
      case 'groq': return new GroqAdapter(provider, this.logger);
      case 'openrouter': return new OpenRouterAdapter(provider, this.logger);
      default: throw new Error(`Unsupported provider type: ${provider.type}`);
    }
  }
}

// ============================================================================
// FACTORY FUNCTIONS
// ============================================================================

export function createUAUIFromExisting(
  providers: ExistingProviderConfig[],
  options: { environment?: string; logLevel?: string; selectionStrategy?: string } = {}
): UAUI {
  const aiProviders: AIProvider[] = providers.map(p => ({
    id: p.id || `${p.type}-${Date.now()}`,
    name: p.name || p.type.toUpperCase(),
    type: p.type,
    config: {
      apiKey: p.apiKey,
      models: [p.defaultModel || getDefaultModel(p.type)],
      defaultModel: p.defaultModel || getDefaultModel(p.type),
      maxTokens: p.maxTokens || 4000,
      temperature: p.temperature || 0.7,
      timeout: p.timeout || 30000
    },
    capabilities: [{
      type: 'chat',
      models: [p.defaultModel || getDefaultModel(p.type)],
      maxTokens: p.maxTokens || 4000,
      streaming: true
    }],
    status: {
      available: true,
      lastChecked: Date.now(),
      latency: 0,
      errorRate: 0
    },
    metrics: {
      totalRequests: 0,
      successRate: 1.0,
      averageLatency: 0,
      totalTokens: 0,
      totalCost: 0,
      lastUsed: 0
    }
  }));

  const config: UAUIConfig = {
    core: {
      version: '1.0.0',
      environment: (options.environment as any) || 'development',
      logLevel: (options.logLevel as any) || 'info'
    },
    ai: {
      providers: aiProviders,
      selectionStrategy: (options.selectionStrategy as any) || 'smart'
    },
    apps: {}
  };

  return new UAUI(config);
}

function getDefaultModel(type: string): string {
  const defaults: Record<string, string> = {
    openai: 'gpt-4',
    groq: 'llama3-70b-8192',
    openrouter: 'openai/gpt-4',
    claude: 'claude-3-opus-20240229',
    gemini: 'gemini-pro',
    mistral: 'mistral-large-latest'
  };
  return defaults[type] || 'default-model';
}

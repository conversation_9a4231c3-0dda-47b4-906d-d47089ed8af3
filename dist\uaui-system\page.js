"use strict";
/**
 * UAUI System Management Page
 * Demonstrates the new navigation system with UAUI integration
 */
"use client";
/**
 * UAUI System Management Page
 * Demonstrates the new navigation system with UAUI integration
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = UAUISystemPage;
const react_1 = __importDefault(require("react"));
const card_1 = require("@/components/ui/card");
const button_1 = require("@/components/ui/button");
const badge_1 = require("@/components/ui/badge");
const alert_1 = require("@/components/ui/alert");
const tabs_1 = require("@/components/ui/tabs");
const lucide_react_1 = require("lucide-react");
const EnhancedDashboardLayout_1 = require("@/components/dashboard/EnhancedDashboardLayout");
const ai_provider_store_1 = require("@/stores/ai-provider-store");
const link_1 = __importDefault(require("next/link"));
const uaui_1 = require("../uaui");
function UAUISystemPage() {
    const { isInitialized, isLoading, error, availableProviders, initialize, reinitialize } = (0, uaui_1.useUAUI)();
    const { providers } = (0, ai_provider_store_1.useAIProviderStore)();
    const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];
    const handleTestUAUI = async () => {
        // Test UAUI functionality
        console.log('Testing UAUI system...');
    };
    const headerActions = (<div className="flex items-center gap-2">
      <link_1.default href="/dashboard/uaui-system/test">
        <button_1.Button variant="default" className="gap-2">
          <lucide_react_1.Beaker className="h-4 w-4"/>
          Test UAUI System
        </button_1.Button>
      </link_1.default>
      <button_1.Button variant="outline" onClick={handleTestUAUI}>
        <lucide_react_1.Activity className="h-4 w-4 mr-2"/>
        Test System
      </button_1.Button>
      <button_1.Button onClick={reinitialize} disabled={isLoading}>
        <lucide_react_1.Sparkles className="h-4 w-4 mr-2"/>
        Reinitialize
      </button_1.Button>
    </div>);
    return (<EnhancedDashboardLayout_1.FullDashboardLayoutWithUAUI pageTitle="UAUI Protocol" pageDescription="Universal AI User Interface Management" headerActions={headerActions}>
      <div className="space-y-6">
        {/* System Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <card_1.Card>
            <card_1.CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <card_1.CardTitle className="text-sm font-medium">System Status</card_1.CardTitle>
              <lucide_react_1.Brain className="h-4 w-4 text-muted-foreground"/>
            </card_1.CardHeader>
            <card_1.CardContent>
              <div className="text-2xl font-bold">
                {isInitialized ? 'Active' : 'Inactive'}
              </div>
              <div className="flex items-center gap-2 mt-2">
                {isInitialized ? (<lucide_react_1.CheckCircle className="h-4 w-4 text-green-500"/>) : (<lucide_react_1.AlertCircle className="h-4 w-4 text-yellow-500"/>)}
                <p className="text-xs text-muted-foreground">
                  {isInitialized ? 'UAUI is running' : 'UAUI needs initialization'}
                </p>
              </div>
            </card_1.CardContent>
          </card_1.Card>

          <card_1.Card>
            <card_1.CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <card_1.CardTitle className="text-sm font-medium">AI Providers</card_1.CardTitle>
              <lucide_react_1.Zap className="h-4 w-4 text-muted-foreground"/>
            </card_1.CardHeader>
            <card_1.CardContent>
              <div className="text-2xl font-bold">{availableProviders.length}</div>
              <p className="text-xs text-muted-foreground">
                {activeProviders.length} configured, {availableProviders.length} active
              </p>
            </card_1.CardContent>
          </card_1.Card>

          <card_1.Card>
            <card_1.CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <card_1.CardTitle className="text-sm font-medium">Performance</card_1.CardTitle>
              <lucide_react_1.BarChart3 className="h-4 w-4 text-muted-foreground"/>
            </card_1.CardHeader>
            <card_1.CardContent>
              <div className="text-2xl font-bold">98.5%</div>
              <p className="text-xs text-muted-foreground">
                Average success rate
              </p>
            </card_1.CardContent>
          </card_1.Card>
        </div>

        {/* Error Alert */}
        {error && (<alert_1.Alert variant="destructive">
            <lucide_react_1.AlertCircle className="h-4 w-4"/>
            <alert_1.AlertDescription>
              {error}
            </alert_1.AlertDescription>
          </alert_1.Alert>)}

        {/* No Providers Warning */}
        {activeProviders.length === 0 && (<alert_1.Alert>
            <lucide_react_1.Info className="h-4 w-4"/>
            <alert_1.AlertDescription>
              No AI providers are configured. Please configure AI providers to enable UAUI functionality.
            </alert_1.AlertDescription>
          </alert_1.Alert>)}

        {/* Main Content Tabs */}
        <tabs_1.Tabs defaultValue="overview" className="space-y-4">
          <tabs_1.TabsList>
            <tabs_1.TabsTrigger value="overview">Overview</tabs_1.TabsTrigger>
            <tabs_1.TabsTrigger value="assistant">AI Assistant</tabs_1.TabsTrigger>
            <tabs_1.TabsTrigger value="providers">Providers</tabs_1.TabsTrigger>
            <tabs_1.TabsTrigger value="settings">Settings</tabs_1.TabsTrigger>
          </tabs_1.TabsList>

          <tabs_1.TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Features Card */}
              <card_1.Card>
                <card_1.CardHeader>
                  <card_1.CardTitle className="flex items-center gap-2">
                    <lucide_react_1.Sparkles className="h-5 w-5 text-purple-500"/>
                    UAUI Features
                  </card_1.CardTitle>
                </card_1.CardHeader>
                <card_1.CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    <lucide_react_1.CheckCircle className="h-4 w-4 text-green-500"/>
                    <span className="text-sm">Smart AI Provider Selection</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <lucide_react_1.CheckCircle className="h-4 w-4 text-green-500"/>
                    <span className="text-sm">AI-Powered Widget Configuration</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <lucide_react_1.CheckCircle className="h-4 w-4 text-green-500"/>
                    <span className="text-sm">Cross-App Action Routing</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <lucide_react_1.CheckCircle className="h-4 w-4 text-green-500"/>
                    <span className="text-sm">Performance Optimization</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <lucide_react_1.CheckCircle className="h-4 w-4 text-green-500"/>
                    <span className="text-sm">Real-time Error Handling</span>
                  </div>
                </card_1.CardContent>
              </card_1.Card>

              {/* Quick Actions */}
              <card_1.Card>
                <card_1.CardHeader>
                  <card_1.CardTitle className="flex items-center gap-2">
                    <lucide_react_1.Settings className="h-5 w-5 text-blue-500"/>
                    Quick Actions
                  </card_1.CardTitle>
                </card_1.CardHeader>
                <card_1.CardContent className="space-y-3">
                  <button_1.Button className="w-full justify-start" variant="outline">
                    <lucide_react_1.Code className="h-4 w-4 mr-2"/>
                    View Integration Code
                  </button_1.Button>
                  <button_1.Button className="w-full justify-start" variant="outline">
                    <lucide_react_1.BarChart3 className="h-4 w-4 mr-2"/>
                    Performance Analytics
                  </button_1.Button>
                  <button_1.Button className="w-full justify-start" variant="outline">
                    <lucide_react_1.Settings className="h-4 w-4 mr-2"/>
                    Configuration Settings
                  </button_1.Button>
                  <button_1.Button className="w-full justify-start" variant="outline">
                    <lucide_react_1.Activity className="h-4 w-4 mr-2"/>
                    System Diagnostics
                  </button_1.Button>
                </card_1.CardContent>
              </card_1.Card>
            </div>
          </tabs_1.TabsContent>

          <tabs_1.TabsContent value="assistant" className="space-y-4">
            <card_1.Card>
              <card_1.CardHeader>
                <card_1.CardTitle>AI Widget Assistant</card_1.CardTitle>
                <p className="text-sm text-muted-foreground">
                  Test the UAUI AI assistant for widget configuration
                </p>
              </card_1.CardHeader>
              <card_1.CardContent>
                <uaui_1.UAUIWidgetAssistant widgetId="demo-widget" onConfigUpdate={(config) => {
            console.log('Config updated:', config);
        }} onActionExecuted={(action) => {
            console.log('Action executed:', action);
        }}/>
              </card_1.CardContent>
            </card_1.Card>
          </tabs_1.TabsContent>

          <tabs_1.TabsContent value="providers" className="space-y-4">
            <uaui_1.UAUIProviderConfig />

            <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg border border-dashed">
              <div>
                <h4 className="font-medium">Need to manage existing providers?</h4>
                <p className="text-sm text-muted-foreground">
                  Configure API keys, test connections, and manage your AI providers
                </p>
              </div>
              <link_1.default href="/dashboard/ai-providers">
                <button_1.Button variant="outline" className="gap-2">
                  <lucide_react_1.Settings className="h-4 w-4"/>
                  Manage Providers
                </button_1.Button>
              </link_1.default>
            </div>

            <card_1.Card>
              <card_1.CardHeader>
                <card_1.CardTitle>Available AI Providers</card_1.CardTitle>
                <p className="text-sm text-muted-foreground">
                  AI providers currently available to UAUI
                </p>
              </card_1.CardHeader>
              <card_1.CardContent>
                {availableProviders.length > 0 ? (<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {availableProviders.map((provider, index) => (<div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-2">
                          <lucide_react_1.Zap className="h-4 w-4 text-green-500"/>
                          <span className="font-medium">{provider}</span>
                        </div>
                        <badge_1.Badge variant="secondary">Active</badge_1.Badge>
                      </div>))}
                  </div>) : (<div className="text-center py-8 text-muted-foreground">
                    No AI providers available. Configure providers in the AI Providers section.
                  </div>)}
              </card_1.CardContent>
            </card_1.Card>
          </tabs_1.TabsContent>

          <tabs_1.TabsContent value="settings" className="space-y-4">
            <card_1.Card>
              <card_1.CardHeader>
                <card_1.CardTitle>UAUI Configuration</card_1.CardTitle>
                <p className="text-sm text-muted-foreground">
                  Configure UAUI system settings and preferences
                </p>
              </card_1.CardHeader>
              <card_1.CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-muted-foreground">
                    Configuration settings will be available here.
                  </div>
                  <button_1.Button variant="outline">
                    <lucide_react_1.Settings className="h-4 w-4 mr-2"/>
                    Open Settings
                  </button_1.Button>
                </div>
              </card_1.CardContent>
            </card_1.Card>
          </tabs_1.TabsContent>
        </tabs_1.Tabs>
      </div>
    </EnhancedDashboardLayout_1.FullDashboardLayoutWithUAUI>);
}
//# sourceMappingURL=page.js.map
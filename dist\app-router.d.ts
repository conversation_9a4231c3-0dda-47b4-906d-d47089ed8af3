/**
 * UAUI App Router
 * Routes actions and events between applications
 */
import { UA<PERSON>Action, CrossAppAction, UAUIEvent } from './types';
import { Logger } from './utils/logger';
import { UAUIApp } from './core';
export interface RouteConfig {
    source: string;
    target: string;
    actionTypes: string[];
    transform?: (action: UAUIAction) => UAUIAction;
    condition?: (action: UAUIAction) => boolean;
}
export declare class AppRouter {
    private logger;
    private apps;
    private routes;
    private actionQueue;
    private isProcessing;
    private isInitialized;
    constructor(logger: Logger);
    initialize(): Promise<void>;
    /**
     * Register an application with the router
     */
    registerApp(appId: string, app: UAUIApp): void;
    /**
     * Unregister an application
     */
    unregisterApp(appId: string): void;
    /**
     * Add a routing rule
     */
    addRoute(config: RouteConfig): void;
    /**
     * Add multiple routing rules
     */
    addRoutes(configs: RouteConfig[]): void;
    /**
     * Get routing rules for a source app
     */
    getRoutes(sourceApp: string): RouteConfig[];
    /**
     * Remove routing rules for a source app
     */
    removeRoutes(sourceApp: string): void;
    /**
     * Route a cross-app action
     */
    routeAction(crossAppAction: CrossAppAction): Promise<void>;
    /**
     * Route an event to target applications
     */
    routeEvent(event: UAUIEvent): Promise<void>;
    /**
     * Get routing statistics
     */
    getStats(): RouterStats;
    /**
     * Get all registered applications
     */
    getRegisteredApps(): string[];
    /**
     * Check if an app is registered
     */
    isAppRegistered(appId: string): boolean;
    /**
     * Shutdown the router
     */
    shutdown(): Promise<void>;
    private startActionProcessor;
    private processActionQueue;
    private executeAction;
    private applyRouteTransforms;
    private executeActionOnApp;
    private handleActionError;
    private broadcastEvent;
    private deliverEventToApp;
    private sleep;
}
export interface RouterStats {
    registeredApps: number;
    activeRoutes: number;
    queuedActions: number;
    isProcessing: boolean;
}
//# sourceMappingURL=app-router.d.ts.map
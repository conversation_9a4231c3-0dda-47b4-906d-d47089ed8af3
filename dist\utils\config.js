"use strict";
/**
 * Configuration Utilities for UAUI Protocol
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createUAUIConfig = createUAUIConfig;
exports.validateConfig = validateConfig;
exports.mergeConfigs = mergeConfigs;
exports.getDefaultConfig = getDefaultConfig;
/**
 * Create a UAUI configuration with defaults
 */
function createUAUIConfig(options = {}) {
    const { environment = 'development', logLevel = 'info', providers = [], apps = {}, selectionStrategy = 'smart', maxConcurrentRequests = 10, defaultTimeout = 30000 } = options;
    return {
        core: {
            version: '1.0.0',
            environment,
            logLevel,
            maxConcurrentRequests,
            defaultTimeout
        },
        ai: {
            providers,
            selectionStrategy,
            fallbackEnabled: true,
            caching: {
                enabled: true,
                ttl: 300000, // 5 minutes
                maxSize: 100
            }
        },
        apps
    };
}
/**
 * Validate UAUI configuration
 */
function validateConfig(config) {
    const errors = [];
    // Validate core configuration
    if (!config.core) {
        errors.push('Missing core configuration');
    }
    else {
        if (!config.core.version) {
            errors.push('Missing core.version');
        }
        if (!config.core.environment) {
            errors.push('Missing core.environment');
        }
        if (!['development', 'staging', 'production'].includes(config.core.environment)) {
            errors.push('Invalid core.environment. Must be development, staging, or production');
        }
        if (!config.core.logLevel) {
            errors.push('Missing core.logLevel');
        }
        if (!['debug', 'info', 'warn', 'error'].includes(config.core.logLevel)) {
            errors.push('Invalid core.logLevel. Must be debug, info, warn, or error');
        }
    }
    // Validate AI configuration
    if (!config.ai) {
        errors.push('Missing ai configuration');
    }
    else {
        if (!Array.isArray(config.ai.providers)) {
            errors.push('ai.providers must be an array');
        }
        else if (config.ai.providers.length === 0) {
            errors.push('At least one AI provider is required');
        }
        if (!config.ai.selectionStrategy) {
            errors.push('Missing ai.selectionStrategy');
        }
        if (!['smart', 'round_robin', 'least_latency'].includes(config.ai.selectionStrategy)) {
            errors.push('Invalid ai.selectionStrategy. Must be smart, round_robin, or least_latency');
        }
    }
    // Validate apps configuration
    if (!config.apps) {
        errors.push('Missing apps configuration');
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
/**
 * Merge configurations with deep merge
 */
function mergeConfigs(base, override) {
    return {
        core: {
            ...base.core,
            ...override.core
        },
        ai: {
            ...base.ai,
            ...override.ai,
            providers: override.ai?.providers || base.ai.providers,
            ...(base.ai.caching || override.ai?.caching ? {
                caching: {
                    enabled: true,
                    ttl: 300000,
                    maxSize: 100,
                    ...base.ai.caching,
                    ...override.ai?.caching
                }
            } : {})
        },
        apps: {
            ...base.apps,
            ...override.apps
        }
    };
}
/**
 * Get default configuration
 */
function getDefaultConfig() {
    return createUAUIConfig();
}
//# sourceMappingURL=config.js.map
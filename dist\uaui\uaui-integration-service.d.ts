/**
 * UAUI Integration Service
 * Bridges Tempo Widget system with UAUI Protocol
 */
import { UAUIResponse } from '..';
interface TempoUAUIConfig {
    environment: 'development' | 'staging' | 'production';
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    selectionStrategy: 'smart' | 'round_robin' | 'least_latency';
}
interface ChatMessage {
    id: string;
    message: string;
    widgetId: string;
    userId?: string;
    sessionId?: string;
}
interface UAUIActionResult {
    success: boolean;
    actionsExecuted: number;
    errors?: string[];
}
declare class UAUIIntegrationService {
    private uaui;
    private isInitialized;
    private config;
    constructor(config?: TempoUAUIConfig);
    /**
     * Initialize UAUI with current AI providers from Tempo Widget
     */
    initialize(): Promise<boolean>;
    /**
     * Process chat message with UAUI intelligence
     */
    processChatMessage(chatMessage: ChatMessage): Promise<UAUIResponse>;
    /**
     * Execute UAUI-generated actions in Tempo Widget context
     */
    private executeActions;
    /**
     * Update widget appearance via API
     */
    private updateWidgetAppearance;
    /**
     * Update widget behavior via API
     */
    private updateWidgetBehavior;
    /**
     * Update widget AI settings via API
     */
    private updateWidgetAI;
    /**
     * Handle cross-app navigation
     */
    private handleNavigation;
    /**
     * Get available AI providers from UAUI
     */
    getAvailableProviders(): Promise<string[]>;
    /**
     * Reinitialize UAUI when providers change
     */
    reinitialize(): Promise<boolean>;
    /**
     * Shutdown UAUI
     */
    shutdown(): Promise<void>;
    /**
     * Get default model for provider type
     */
    private getDefaultModel;
    /**
     * Check if UAUI is ready
     */
    isReady(): boolean;
    /**
     * Get UAUI configuration
     */
    getConfig(): TempoUAUIConfig;
}
declare const uauiIntegrationService: UAUIIntegrationService;
export { uauiIntegrationService, type TempoUAUIConfig, type ChatMessage, type UAUIActionResult };
export default uauiIntegrationService;
//# sourceMappingURL=uaui-integration-service.d.ts.map
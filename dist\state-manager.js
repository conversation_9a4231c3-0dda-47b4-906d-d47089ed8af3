"use strict";
/**
 * UAUI State Manager
 * Handles cross-app state synchronization and persistence
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.StateManager = void 0;
class StateManager {
    constructor(logger) {
        this.appStates = new Map();
        this.stateHistory = new Map();
        this.syncSubscriptions = new Map();
        this.maxHistorySize = 50;
        this.isInitialized = false;
        this.logger = logger;
    }
    async initialize() {
        if (this.isInitialized)
            return;
        this.logger.info('Initializing State Manager...');
        this.isInitialized = true;
        this.logger.info('State Manager initialized');
    }
    /**
     * Set state for an application
     */
    async setState(appId, state, options) {
        try {
            const previousState = this.appStates.get(appId);
            const snapshot = this.createSnapshot(appId, state);
            // Update current state
            this.appStates.set(appId, state);
            // Add to history
            this.addToHistory(appId, snapshot);
            // Notify subscribers
            await this.notifySubscribers(appId, state, previousState);
            this.logger.debug(`State updated for app: ${appId}`, { version: snapshot.version });
        }
        catch (error) {
            this.logger.error(`Failed to set state for app: ${appId}`, error);
            throw error;
        }
    }
    /**
     * Get current state for an application
     */
    getState(appId) {
        return this.appStates.get(appId);
    }
    /**
     * Get state history for an application
     */
    getStateHistory(appId, limit) {
        const history = this.stateHistory.get(appId) || [];
        return limit ? history.slice(-limit) : [...history];
    }
    /**
     * Synchronize state between applications
     */
    async sync(fromApp, toApp, state, options) {
        try {
            const sourceState = state || this.appStates.get(fromApp);
            if (!sourceState) {
                throw new Error(`No state found for source app: ${fromApp}`);
            }
            let syncState = sourceState;
            // Apply transformations if specified
            if (options?.transform) {
                syncState = options.transform(sourceState);
            }
            // Apply selective sync if specified
            if (options?.selective) {
                syncState = this.selectiveSync(sourceState, options.selective);
            }
            // Update target app state
            await this.setState(toApp, syncState, options);
            this.logger.info(`State synced from ${fromApp} to ${toApp}`);
        }
        catch (error) {
            this.logger.error(`Failed to sync state from ${fromApp} to ${toApp}`, error);
            throw error;
        }
    }
    /**
     * Subscribe to state changes for an application
     */
    subscribe(appId, callback) {
        if (!this.syncSubscriptions.has(appId)) {
            this.syncSubscriptions.set(appId, new Set());
        }
        this.syncSubscriptions.get(appId).add(callback);
        this.logger.debug(`Subscription added for app: ${appId}`);
        // Return unsubscribe function
        return () => {
            this.syncSubscriptions.get(appId)?.delete(callback);
            if (this.syncSubscriptions.get(appId)?.size === 0) {
                this.syncSubscriptions.delete(appId);
            }
        };
    }
    /**
     * Merge states from multiple applications
     */
    async mergeStates(appIds, strategy = 'latest') {
        const states = appIds.map(appId => ({
            appId,
            state: this.appStates.get(appId),
            timestamp: this.getLatestSnapshot(appId)?.timestamp || 0
        })).filter(item => item.state);
        if (states.length === 0) {
            return {};
        }
        switch (strategy) {
            case 'latest':
                return this.mergeByLatest(states);
            case 'deep_merge':
                return this.deepMergeStates(states);
            default:
                return this.mergeByLatest(states);
        }
    }
    /**
     * Restore state from a specific snapshot
     */
    async restoreState(appId, snapshotId) {
        const history = this.stateHistory.get(appId) || [];
        const snapshot = history.find(s => s.id === snapshotId);
        if (!snapshot) {
            throw new Error(`Snapshot not found: ${snapshotId}`);
        }
        await this.setState(appId, snapshot.state);
        this.logger.info(`State restored for app: ${appId} from snapshot: ${snapshotId}`);
    }
    /**
     * Clear state for an application
     */
    async clearState(appId) {
        this.appStates.delete(appId);
        this.stateHistory.delete(appId);
        this.syncSubscriptions.delete(appId);
        this.logger.info(`State cleared for app: ${appId}`);
    }
    /**
     * Get all application states
     */
    getAllStates() {
        const result = {};
        for (const [appId, state] of this.appStates) {
            result[appId] = state;
        }
        return result;
    }
    /**
     * Shutdown the state manager
     */
    async shutdown() {
        this.logger.info('Shutting down State Manager...');
        this.appStates.clear();
        this.stateHistory.clear();
        this.syncSubscriptions.clear();
        this.isInitialized = false;
        this.logger.info('State Manager shut down');
    }
    // Private methods
    createSnapshot(appId, state) {
        const history = this.stateHistory.get(appId) || [];
        const version = history.length + 1;
        return {
            id: `snapshot-${appId}-${Date.now()}`,
            appId,
            timestamp: Date.now(),
            state: this.deepClone(state),
            version,
            checksum: this.calculateChecksum(state)
        };
    }
    addToHistory(appId, snapshot) {
        if (!this.stateHistory.has(appId)) {
            this.stateHistory.set(appId, []);
        }
        const history = this.stateHistory.get(appId);
        history.push(snapshot);
        // Trim history if it exceeds max size
        if (history.length > this.maxHistorySize) {
            history.splice(0, history.length - this.maxHistorySize);
        }
    }
    async notifySubscribers(appId, newState, previousState) {
        const subscribers = this.syncSubscriptions.get(appId);
        if (!subscribers || subscribers.size === 0) {
            return;
        }
        const changeEvent = {
            appId,
            newState,
            previousState,
            timestamp: Date.now()
        };
        for (const callback of subscribers) {
            try {
                await callback(changeEvent);
            }
            catch (error) {
                this.logger.error(`Subscriber error for app: ${appId}`, error);
            }
        }
    }
    selectiveSync(state, keys) {
        const result = {};
        for (const key of keys) {
            if (key in state) {
                result[key] = state[key];
            }
        }
        return result;
    }
    getLatestSnapshot(appId) {
        const history = this.stateHistory.get(appId);
        return history && history.length > 0 ? history[history.length - 1] : undefined;
    }
    mergeByLatest(states) {
        return states.sort((a, b) => b.timestamp - a.timestamp)[0].state;
    }
    deepMergeStates(states) {
        let merged = {};
        // Sort by timestamp (oldest first)
        const sortedStates = states.sort((a, b) => a.timestamp - b.timestamp);
        for (const { state } of sortedStates) {
            merged = this.deepMerge(merged, state);
        }
        return merged;
    }
    deepMerge(target, source) {
        if (source === null || typeof source !== 'object') {
            return source;
        }
        if (Array.isArray(source)) {
            return [...source];
        }
        const result = { ...target };
        for (const key in source) {
            if (source.hasOwnProperty(key)) {
                if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
                    result[key] = this.deepMerge(result[key] || {}, source[key]);
                }
                else {
                    result[key] = source[key];
                }
            }
        }
        return result;
    }
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        if (obj instanceof Date) {
            return new Date(obj.getTime());
        }
        if (Array.isArray(obj)) {
            return obj.map(item => this.deepClone(item));
        }
        const cloned = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                cloned[key] = this.deepClone(obj[key]);
            }
        }
        return cloned;
    }
    calculateChecksum(state) {
        // Simple checksum calculation
        const str = JSON.stringify(state);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return hash.toString(16);
    }
}
exports.StateManager = StateManager;
//# sourceMappingURL=state-manager.js.map
{"version": 3, "file": "use-uaui.js", "sourceRoot": "", "sources": ["../../src/uaui/use-uaui.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;AAmCH,0BAoLC;AAKD,kCAuBC;AAjPD,iCAAiE;AACjE,kEAAgE;AAChE,0FAGoC;AA4BpC,SAAgB,OAAO,CAAC,UAA0B,EAAE;IAClD,MAAM,EACJ,cAAc,GAAG,IAAI,EACrB,WAAW,GAAG,aAAa,EAC3B,QAAQ,GAAG,MAAM,EAClB,GAAG,OAAO,CAAC;IAEZ,QAAQ;IACR,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAC1D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,IAAA,gBAAQ,EAAC,KAAK,CAAC,CAAC;IAClD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,IAAA,gBAAQ,EAAgB,IAAI,CAAC,CAAC;IACxD,MAAM,CAAC,kBAAkB,EAAE,qBAAqB,CAAC,GAAG,IAAA,gBAAQ,EAAW,EAAE,CAAC,CAAC;IAE3E,OAAO;IACP,MAAM,uBAAuB,GAAG,IAAA,cAAM,EAAC,KAAK,CAAC,CAAC;IAE9C,oBAAoB;IACpB,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,IAAA,sCAAkB,GAAE,CAAC;IAE3D;;OAEG;IACH,MAAM,UAAU,GAAG,IAAA,mBAAW,EAAC,KAAK,IAAsB,EAAE;QAC1D,IAAI,SAAS;YAAE,OAAO,KAAK,CAAC;QAE5B,YAAY,CAAC,IAAI,CAAC,CAAC;QACnB,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEf,IAAI,CAAC;YACH,kCAAkC;YAClC,MAAM,cAAc,EAAE,CAAC;YAEvB,0BAA0B;YAC1B,MAAM,OAAO,GAAG,MAAM,kCAAsB,CAAC,UAAU,EAAE,CAAC;YAE1D,IAAI,OAAO,EAAE,CAAC;gBACZ,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBAEvB,0BAA0B;gBAC1B,MAAM,SAAS,GAAG,MAAM,kCAAsB,CAAC,qBAAqB,EAAE,CAAC;gBACvE,qBAAqB,CAAC,SAAS,CAAC,CAAC;gBAEjC,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,0DAA0D,CAAC,CAAC;YACvE,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,IAAI,2BAA2B,CAAC;YAChE,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC;YACpD,OAAO,KAAK,CAAC;QACf,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,KAAK,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC;IAEhC;;OAEG;IACH,MAAM,kBAAkB,GAAG,IAAA,mBAAW,EAAC,KAAK,EAC1C,OAAoB,EACU,EAAE;QAChC,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,QAAQ,CAAC,sBAAsB,CAAC,CAAC;YACjC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC;YACH,QAAQ,CAAC,IAAI,CAAC,CAAC;YACf,MAAM,QAAQ,GAAG,MAAM,kCAAsB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAE1E,OAAO,CAAC,GAAG,CAAC,2BAA2B,EAAE;gBACvC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,QAAQ;gBACpC,YAAY,EAAE,QAAQ,CAAC,QAAQ,CAAC,YAAY;gBAC5C,OAAO,EAAE,QAAQ,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC;aACvC,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,IAAI,2BAA2B,CAAC;YAChE,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC;IAEpB;;OAEG;IACH,MAAM,YAAY,GAAG,IAAA,mBAAW,EAAC,KAAK,IAAsB,EAAE;QAC5D,gBAAgB,CAAC,KAAK,CAAC,CAAC;QACxB,qBAAqB,CAAC,EAAE,CAAC,CAAC;QAC1B,OAAO,MAAM,UAAU,EAAE,CAAC;IAC5B,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;IAEjB;;OAEG;IACH,MAAM,QAAQ,GAAG,IAAA,mBAAW,EAAC,KAAK,IAAmB,EAAE;QACrD,IAAI,CAAC;YACH,MAAM,kCAAsB,CAAC,QAAQ,EAAE,CAAC;YACxC,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACxB,qBAAqB,CAAC,EAAE,CAAC,CAAC;YAC1B,QAAQ,CAAC,IAAI,CAAC,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC;QAChD,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP;;OAEG;IACH,MAAM,UAAU,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE;QAClC,QAAQ,CAAC,IAAI,CAAC,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP;;OAEG;IACH,MAAM,OAAO,GAAG,IAAA,mBAAW,EAAC,GAAY,EAAE;QACxC,OAAO,kCAAsB,CAAC,OAAO,EAAE,CAAC;IAC1C,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP;;OAEG;IACH,MAAM,SAAS,GAAG,IAAA,mBAAW,EAAC,GAAG,EAAE;QACjC,OAAO,kCAAsB,CAAC,SAAS,EAAE,CAAC;IAC5C,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,2BAA2B;IAC3B,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,IAAI,cAAc,IAAI,CAAC,uBAAuB,CAAC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3G,uBAAuB,CAAC,OAAO,GAAG,IAAI,CAAC;YACvC,UAAU,EAAE,CAAC;QACf,CAAC;IACH,CAAC,EAAE,CAAC,cAAc,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;IAE5C,8BAA8B;IAC9B,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,IAAI,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtE,qCAAqC;YACrC,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAC5G,IAAI,eAAe,CAAC,MAAM,KAAK,kBAAkB,CAAC,MAAM,EAAE,CAAC;gBACzD,OAAO,CAAC,GAAG,CAAC,iDAAiD,CAAC,CAAC;gBAC/D,YAAY,EAAE,CAAC;YACjB,CAAC;QACH,CAAC;IACH,CAAC,EAAE,CAAC,SAAS,EAAE,aAAa,EAAE,kBAAkB,EAAE,YAAY,CAAC,CAAC,CAAC;IAEjE,qBAAqB;IACrB,IAAA,iBAAS,EAAC,GAAG,EAAE;QACb,OAAO,GAAG,EAAE;YACV,IAAI,aAAa,EAAE,CAAC;gBAClB,QAAQ,EAAE,CAAC;YACb,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC;IAE9B,OAAO;QACL,QAAQ;QACR,aAAa;QACb,SAAS;QACT,KAAK;QACL,kBAAkB;QAElB,UAAU;QACV,UAAU;QACV,kBAAkB;QAClB,YAAY;QACZ,QAAQ;QACR,UAAU;QAEV,YAAY;QACZ,OAAO;QACP,SAAS;KACV,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,WAAW,CAAC,QAAgB;IAC1C,MAAM,IAAI,GAAG,OAAO,EAAE,CAAC;IAEvB,MAAM,WAAW,GAAG,IAAA,mBAAW,EAAC,KAAK,EACnC,OAAe,EACf,MAAe,EACf,SAAkB,EACY,EAAE;QAChC,MAAM,WAAW,GAAgB;YAC/B,EAAE,EAAE,OAAO,IAAI,CAAC,GAAG,EAAE,EAAE;YACvB,OAAO;YACP,QAAQ;YACR,MAAM;YACN,SAAS;SACV,CAAC;QAEF,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;IACpD,CAAC,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;IAErB,OAAO;QACL,GAAG,IAAI;QACP,WAAW;KACZ,CAAC;AACJ,CAAC;AAED,kBAAe,OAAO,CAAC"}
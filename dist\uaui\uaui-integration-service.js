"use strict";
/**
 * UAUI Integration Service
 * Bridges Tempo Widget system with UAUI Protocol
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uauiIntegrationService = void 0;
const __1 = require("..");
const ai_provider_store_1 = require("@/stores/ai-provider-store");
const api_client_1 = __importDefault(require("@/lib/api-client"));
class UAUIIntegrationService {
    constructor(config = {
        environment: 'development',
        logLevel: 'info',
        selectionStrategy: 'smart'
    }) {
        this.uaui = null;
        this.isInitialized = false;
        this.config = config;
    }
    /**
     * Initialize UAUI with current AI providers from Tempo Widget
     */
    async initialize() {
        try {
            // Get AI providers from Tempo Widget store
            const { providers } = ai_provider_store_1.useAIProviderStore.getState();
            // Filter active and configured providers
            const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];
            if (activeProviders.length === 0) {
                console.warn('No active AI providers found for UAUI initialization');
                return false;
            }
            // Convert Tempo providers to UAUI format
            const uauiProviders = activeProviders.map(provider => ({
                id: provider.id,
                name: provider.name,
                type: provider.type,
                apiKey: provider.apiKey,
                defaultModel: this.getDefaultModel(provider.type),
                maxTokens: 4000,
                temperature: 0.7,
                timeout: 30000
            }));
            // Create UAUI instance
            this.uaui = (0, __1.createUAUIFromExisting)(uauiProviders, {
                environment: this.config.environment,
                logLevel: this.config.logLevel,
                selectionStrategy: this.config.selectionStrategy
            });
            // Initialize UAUI
            await this.uaui.initialize();
            this.isInitialized = true;
            console.log(`✅ UAUI initialized with ${activeProviders.length} providers`);
            return true;
        }
        catch (error) {
            console.error('Failed to initialize UAUI:', error);
            return false;
        }
    }
    /**
     * Process chat message with UAUI intelligence
     */
    async processChatMessage(chatMessage) {
        if (!this.isInitialized || !this.uaui) {
            throw new Error('UAUI not initialized. Call initialize() first.');
        }
        const request = {
            id: `chat-${Date.now()}`,
            type: 'ai.request',
            message: chatMessage.message,
            context: {
                app: 'tempo-widget',
                widgetId: chatMessage.widgetId,
                userId: chatMessage.userId,
                sessionId: chatMessage.sessionId,
                timestamp: Date.now()
            }
        };
        try {
            const response = await this.uaui.processAIRequest(request);
            // Execute any generated actions
            if (response.actions && response.actions.length > 0) {
                await this.executeActions(response.actions, chatMessage.widgetId);
            }
            return response;
        }
        catch (error) {
            console.error('UAUI chat processing failed:', error);
            throw error;
        }
    }
    /**
     * Execute UAUI-generated actions in Tempo Widget context
     */
    async executeActions(actions, widgetId) {
        const results = {
            success: true,
            actionsExecuted: 0,
            errors: []
        };
        for (const action of actions) {
            try {
                switch (action.type) {
                    case 'widget.appearance.update':
                        await this.updateWidgetAppearance(widgetId, action.payload);
                        break;
                    case 'widget.behavior.update':
                        await this.updateWidgetBehavior(widgetId, action.payload);
                        break;
                    case 'cross_app.navigate':
                        await this.handleNavigation(action.payload);
                        break;
                    case 'widget.ai.update':
                        await this.updateWidgetAI(widgetId, action.payload);
                        break;
                    default:
                        console.warn(`Unknown action type: ${action.type}`);
                }
                results.actionsExecuted++;
            }
            catch (error) {
                console.error(`Failed to execute action ${action.type}:`, error);
                results.errors?.push(`${action.type}: ${error}`);
                results.success = false;
            }
        }
        return results;
    }
    /**
     * Update widget appearance via API
     */
    async updateWidgetAppearance(widgetId, appearance) {
        await api_client_1.default.put(`/widgets/${widgetId}/appearance`, appearance);
        console.log(`✅ Updated widget ${widgetId} appearance:`, appearance);
    }
    /**
     * Update widget behavior via API
     */
    async updateWidgetBehavior(widgetId, behavior) {
        await api_client_1.default.put(`/widgets/${widgetId}/behavior`, behavior);
        console.log(`✅ Updated widget ${widgetId} behavior:`, behavior);
    }
    /**
     * Update widget AI settings via API
     */
    async updateWidgetAI(widgetId, aiSettings) {
        await api_client_1.default.put(`/widgets/${widgetId}/ai-settings`, aiSettings);
        console.log(`✅ Updated widget ${widgetId} AI settings:`, aiSettings);
    }
    /**
     * Handle cross-app navigation
     */
    async handleNavigation(payload) {
        // Emit navigation event for frontend to handle
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('uaui-navigate', {
                detail: payload
            }));
        }
        console.log(`✅ Navigation triggered:`, payload);
    }
    /**
     * Get available AI providers from UAUI
     */
    async getAvailableProviders() {
        if (!this.uaui)
            return [];
        return await this.uaui.getAvailableProviders();
    }
    /**
     * Reinitialize UAUI when providers change
     */
    async reinitialize() {
        if (this.uaui) {
            await this.uaui.shutdown();
            this.isInitialized = false;
        }
        return await this.initialize();
    }
    /**
     * Shutdown UAUI
     */
    async shutdown() {
        if (this.uaui) {
            await this.uaui.shutdown();
            this.uaui = null;
            this.isInitialized = false;
        }
    }
    /**
     * Get default model for provider type
     */
    getDefaultModel(type) {
        const defaults = {
            openai: 'gpt-4',
            groq: 'llama3-70b-8192',
            openrouter: 'openai/gpt-4',
            claude: 'claude-3-opus-20240229',
            gemini: 'gemini-pro',
            mistral: 'mistral-large-latest'
        };
        return defaults[type] || 'default-model';
    }
    /**
     * Check if UAUI is ready
     */
    isReady() {
        return this.isInitialized && this.uaui !== null;
    }
    /**
     * Get UAUI configuration
     */
    getConfig() {
        return this.config;
    }
}
// Create singleton instance
const uauiIntegrationService = new UAUIIntegrationService();
exports.uauiIntegrationService = uauiIntegrationService;
exports.default = uauiIntegrationService;
//# sourceMappingURL=uaui-integration-service.js.map
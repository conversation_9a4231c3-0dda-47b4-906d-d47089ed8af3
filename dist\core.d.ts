/**
 * Universal AI-UI Protocol (UAUI) - Core Engine
 * Production-ready, independent protocol implementation
 */
import { EventEmitter } from 'events';
import { UAUIConfig, UAUIRequest, UAUIResponse, CrossAppAction, UAUIAppConfig } from './types';
import { EventBus } from './event-bus';
import { UniversalAIService } from './ai-service';
export declare class UAUICore extends EventEmitter {
    private config;
    private eventBus;
    private stateManager;
    private appRouter;
    private aiService;
    private logger;
    private apps;
    private isInitialized;
    constructor(config: UAUIConfig);
    /**
     * Initialize the UAUI core engine
     */
    initialize(): Promise<void>;
    /**
     * Register a new application with the UAUI protocol
     */
    registerApp(appId: string, config: UAUIAppConfig): Promise<UAUIApp>;
    /**
     * Process an AI request through the UAUI protocol
     */
    processAIRequest(request: UAUIRequest): Promise<UAUIResponse>;
    /**
     * Synchronize state between applications
     */
    syncState(fromApp: string, toApp: string, state: any): Promise<void>;
    /**
     * Execute a cross-application action
     */
    executeCrossAppAction(crossAppAction: CrossAppAction): Promise<void>;
    /**
     * Get application instance
     */
    getApp(appId: string): UAUIApp | undefined;
    /**
     * Get all registered applications
     */
    getApps(): Map<string, UAUIApp>;
    /**
     * Get UAUI configuration
     */
    getConfig(): UAUIConfig;
    /**
     * Get AI service instance
     */
    getAIService(): UniversalAIService;
    /**
     * Get event bus instance
     */
    getEventBus(): EventBus;
    /**
     * Shutdown the UAUI core engine
     */
    shutdown(): Promise<void>;
    private setupEventHandlers;
    private handleAIRequest;
    private handleCrossAppAction;
    private handleStateSync;
    private validateRequest;
    private enrichContext;
    private planActions;
}
/**
 * UAUI Application wrapper
 */
export declare class UAUIApp {
    private id;
    private config;
    private core;
    private isInitialized;
    constructor(id: string, config: UAUIAppConfig, core: UAUICore);
    initialize(): Promise<void>;
    shutdown(): Promise<void>;
    getId(): string;
    getConfig(): UAUIAppConfig;
    private handleEvent;
}
//# sourceMappingURL=core.d.ts.map
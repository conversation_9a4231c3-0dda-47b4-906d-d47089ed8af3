/**
 * UAUI Protocol - Universal AI User Interface
 * Standalone Open Source AI Provider Management System
 */

// Core exports
export { UAUI, createUAUIFromExisting } from './uaui';

// Type exports
export type {
  UAUIConfig,
  UAUIRequest,
  UAUIResponse,
  UAUIAction,
  AIProvider,
  AIProviderType,
  AIProviderConfig,
  RequestContext,
  ResponseMetadata,
  UAUIError,
  ExistingProviderConfig,
  TempoWidgetConfig,
  TempoAIProvider,
  TempoAPIClient
} from './types';

// Version
export const VERSION = '1.0.0';
export const PROTOCOL_VERSION = '1.0';

// Utility functions
export function getVersion(): string {
  return VERSION;
}

export function getProtocolVersion(): string {
  return PROTOCOL_VERSION;
}

export function isCompatible(version: string): boolean {
  const [major] = version.split('.');
  const [currentMajor] = PROTOCOL_VERSION.split('.');
  return major === currentMajor;
}

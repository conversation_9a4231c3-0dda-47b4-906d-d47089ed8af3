/**
 * UAUI System - Main Export File
 * Exports all necessary components and types for integration
 */

// Core UAUI exports
export { UAUI, createUAUIFromExisting } from './uaui';
export { UAUICore } from './core';
export { UAUIFactory } from './factory';
export { EventBus } from './event-bus';
export { StateManager } from './state-manager';
export { AppRouter } from './app-router';

// AI Service exports
export {
  UniversalAIService,
  AIProviderAdapter,
  OpenAIAdapter,
  ClaudeAdapter,
  GeminiAdapter,
  MistralAdapter,
  GroqAdapter
} from './ai-service';

// Type exports
export type {
  UAUIConfig,
  UAUIAppConfig,
  UAUIRequest,
  UAUIResponse,
  AIConfig,
  AIProvider,
  AIProviderType,
  RequestContext,
  ResponseMetadata,
  UAUIError,
  ExistingProviderConfig
} from './types';

// Utility exports
export { Logger } from './utils/logger';
export {
  createUAUIConfig,
  validateConfig,
  mergeConfigs,
  getDefaultConfig
} from './utils/config';

// Version
export const VERSION = '1.0.0';

// Default configuration
export const DEFAULT_CONFIG = {
  core: {
    version: VERSION,
    environment: 'development',
    logLevel: 'info',
    maxConcurrentRequests: 10,
    defaultTimeout: 30000
  },
  ai: {
    providers: [],
    selectionStrategy: 'smart',
    fallbackEnabled: true,
    caching: {
      enabled: true,
      ttl: 300000, // 5 minutes
      maxSize: 100
    }
  },
  apps: {}
};

/**
 * UAUI Module Index
 * Centralized exports for the Universal AI User Interface integration
 */

// Hooks
export { useUAUI } from './hooks/use-uaui';

// Services
export { default as uauiIntegrationService } from './services/uaui-integration-service';
export type {
  ChatMessage,
  UAUIActionResult,
  TempoUAUIConfig
} from './services/uaui-integration-service';

// Components (re-export from components/uaui)
export {
  UAUIStatusCard,
  UAUIProviderConfig,
  UAUIFloatingButton,
  UAUINotificationBanner,
  UAUINavigationGuide,
  UAUIWidgetAssistant
} from '../../components/uaui';

// Types
export type { UseUAUIOptions, UseUAUIReturn } from './hooks/use-uaui';

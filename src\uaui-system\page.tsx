/**
 * UAUI System Management Page
 * Demonstrates the new navigation system with UAUI integration
 */

"use client";

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Brain,
  Sparkles,
  Settings,
  Activity,
  BarChart3,
  Code,
  Zap,
  CheckCircle,
  AlertCircle,
  Info,
  Beaker
} from 'lucide-react';
import { FullDashboardLayoutWithUAUI } from '@/components/dashboard/EnhancedDashboardLayout';
import { useAIProviderStore } from '@/stores/ai-provider-store';
import Link from 'next/link';
import { useUAUI, UAUIProviderConfig, UAUIWidgetAssistant } from '../uaui';

export default function UAUISystemPage() {
  const {
    isInitialized,
    isLoading,
    error,
    availableProviders,
    initialize,
    reinitialize
  } = useUAUI();

  const { providers } = useAIProviderStore();
  const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];

  const handleTestUAUI = async () => {
    // Test UAUI functionality
    console.log('Testing UAUI system...');
  };

  const headerActions = (
    <div className="flex items-center gap-2">
      <Link href="/dashboard/uaui-system/test">
        <Button variant="default" className="gap-2">
          <Beaker className="h-4 w-4" />
          Test UAUI System
        </Button>
      </Link>
      <Button variant="outline" onClick={handleTestUAUI}>
        <Activity className="h-4 w-4 mr-2" />
        Test System
      </Button>
      <Button onClick={reinitialize} disabled={isLoading}>
        <Sparkles className="h-4 w-4 mr-2" />
        Reinitialize
      </Button>
    </div>
  );

  return (
    <FullDashboardLayoutWithUAUI
      pageTitle="UAUI Protocol"
      pageDescription="Universal AI User Interface Management"
      headerActions={headerActions}
    >
      <div className="space-y-6">
        {/* System Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">System Status</CardTitle>
              <Brain className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isInitialized ? 'Active' : 'Inactive'}
              </div>
              <div className="flex items-center gap-2 mt-2">
                {isInitialized ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-yellow-500" />
                )}
                <p className="text-xs text-muted-foreground">
                  {isInitialized ? 'UAUI is running' : 'UAUI needs initialization'}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">AI Providers</CardTitle>
              <Zap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{availableProviders.length}</div>
              <p className="text-xs text-muted-foreground">
                {activeProviders.length} configured, {availableProviders.length} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Performance</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">98.5%</div>
              <p className="text-xs text-muted-foreground">
                Average success rate
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* No Providers Warning */}
        {activeProviders.length === 0 && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              No AI providers are configured. Please configure AI providers to enable UAUI functionality.
            </AlertDescription>
          </Alert>
        )}

        {/* Main Content Tabs */}
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="assistant">AI Assistant</TabsTrigger>
            <TabsTrigger value="providers">Providers</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Features Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-purple-500" />
                    UAUI Features
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Smart AI Provider Selection</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">AI-Powered Widget Configuration</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Cross-App Action Routing</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Performance Optimization</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm">Real-time Error Handling</span>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5 text-blue-500" />
                    Quick Actions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full justify-start" variant="outline">
                    <Code className="h-4 w-4 mr-2" />
                    View Integration Code
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Performance Analytics
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <Settings className="h-4 w-4 mr-2" />
                    Configuration Settings
                  </Button>
                  <Button className="w-full justify-start" variant="outline">
                    <Activity className="h-4 w-4 mr-2" />
                    System Diagnostics
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="assistant" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>AI Widget Assistant</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Test the UAUI AI assistant for widget configuration
                </p>
              </CardHeader>
              <CardContent>
                <UAUIWidgetAssistant
                  widgetId="demo-widget"
                  onConfigUpdate={(config) => {
                    console.log('Config updated:', config);
                  }}
                  onActionExecuted={(action) => {
                    console.log('Action executed:', action);
                  }}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="providers" className="space-y-4">
            <UAUIProviderConfig />

            <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg border border-dashed">
              <div>
                <h4 className="font-medium">Need to manage existing providers?</h4>
                <p className="text-sm text-muted-foreground">
                  Configure API keys, test connections, and manage your AI providers
                </p>
              </div>
              <Link href="/dashboard/ai-providers">
                <Button variant="outline" className="gap-2">
                  <Settings className="h-4 w-4" />
                  Manage Providers
                </Button>
              </Link>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Available AI Providers</CardTitle>
                <p className="text-sm text-muted-foreground">
                  AI providers currently available to UAUI
                </p>
              </CardHeader>
              <CardContent>
                {availableProviders.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {availableProviders.map((provider, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-2">
                          <Zap className="h-4 w-4 text-green-500" />
                          <span className="font-medium">{provider}</span>
                        </div>
                        <Badge variant="secondary">Active</Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No AI providers available. Configure providers in the AI Providers section.
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>UAUI Configuration</CardTitle>
                <p className="text-sm text-muted-foreground">
                  Configure UAUI system settings and preferences
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-muted-foreground">
                    Configuration settings will be available here.
                  </div>
                  <Button variant="outline">
                    <Settings className="h-4 w-4 mr-2" />
                    Open Settings
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </FullDashboardLayoutWithUAUI>
  );
}

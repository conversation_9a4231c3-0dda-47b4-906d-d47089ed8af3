/**
 * UAUI React Hook
 * Provides React integration for the Universal AI User Interface system
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAIProviderStore } from '@/stores/ai-provider-store';
import uauiIntegrationService, {
  type ChatMessage,
  type UAUIActionResult
} from '../services/uaui-integration-service';
import type { UAUIResponse } from '../..';

interface UseUAUIOptions {
  autoInitialize?: boolean;
  environment?: 'development' | 'production';
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
}

interface UseUAUIReturn {
  // State
  isInitialized: boolean;
  isLoading: boolean;
  error: string | null;
  availableProviders: string[];

  // Actions
  initialize: () => Promise<boolean>;
  reinitialize: () => Promise<boolean>;
  shutdown: () => Promise<void>;
  clearError: () => void;

  // Messaging
  sendMessage: (message: ChatMessage) => Promise<UAUIResponse | null>;

  // Utilities
  isReady: () => boolean;
  getStatus: () => {
    initialized: boolean;
    loading: boolean;
    error: string | null;
    providers: string[];
  };
}

/**
 * React hook for UAUI integration
 */
export function useUAUI(options: UseUAUIOptions = {}): UseUAUIReturn {
  const {
    autoInitialize = true,
    environment = 'development',
    logLevel = 'info'
  } = options;

  // State
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [availableProviders, setAvailableProviders] = useState<string[]>([]);

  // Refs
  const initializationAttempted = useRef(false);

  // AI Provider store
  const { providers, fetchProviders } = useAIProviderStore();

  /**
   * Initialize UAUI
   */
  const initialize = useCallback(async (): Promise<boolean> => {
    if (isLoading) return false;

    setIsLoading(true);
    setError(null);

    try {
      console.log('🚀 Initializing UAUI system...');

      const success = await uauiIntegrationService.initialize();

      if (success) {
        setIsInitialized(true);

        // Get available providers
        const providers = uauiIntegrationService.getAvailableProviders();
        setAvailableProviders(providers);

        console.log('✅ UAUI initialized successfully');
        console.log(`📊 Available providers: ${providers.join(', ')}`);
      } else {
        setError('Failed to initialize UAUI system');
        console.error('❌ UAUI initialization failed');
      }

      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('❌ UAUI initialization error:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [isLoading]);

  /**
   * Reinitialize UAUI
   */
  const reinitialize = useCallback(async (): Promise<boolean> => {
    console.log('🔄 Reinitializing UAUI system...');

    try {
      await shutdown();
      return await initialize();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Reinitialization failed';
      setError(errorMessage);
      console.error('❌ UAUI reinitialization error:', err);
      return false;
    }
  }, [initialize]);

  /**
   * Shutdown UAUI
   */
  const shutdown = useCallback(async (): Promise<void> => {
    console.log('🛑 Shutting down UAUI system...');

    try {
      await uauiIntegrationService.shutdown();
      setIsInitialized(false);
      setAvailableProviders([]);
      setError(null);
      console.log('✅ UAUI shutdown complete');
    } catch (err) {
      console.error('❌ UAUI shutdown error:', err);
    }
  }, []);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Send message to UAUI
   */
  const sendMessage = useCallback(async (message: ChatMessage): Promise<UAUIResponse | null> => {
    if (!isInitialized) {
      throw new Error('UAUI is not initialized');
    }

    try {
      const response = await uauiIntegrationService.sendMessage(message);
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      setError(errorMessage);
      console.error('❌ UAUI message error:', err);
      return null;
    }
  }, [isInitialized]);

  /**
   * Check if UAUI is ready
   */
  const isReady = useCallback((): boolean => {
    return uauiIntegrationService.isReady();
  }, []);

  /**
   * Get current status
   */
  const getStatus = useCallback(() => {
    return {
      initialized: isInitialized,
      loading: isLoading,
      error,
      providers: availableProviders
    };
  }, [isInitialized, isLoading, error, availableProviders]);

  // Auto-initialize on mount
  useEffect(() => {
    if (autoInitialize && !initializationAttempted.current && Array.isArray(providers) && providers.length > 0) {
      initializationAttempted.current = true;
      initialize();
    }
  }, [autoInitialize, initialize, providers]);

  // Listen for provider changes
  useEffect(() => {
    if (isInitialized && Array.isArray(providers) && providers.length > 0) {
      // Reinitialize when providers change
      const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];
      if (activeProviders.length !== availableProviders.length) {
        console.log('🔄 AI providers changed, reinitializing UAUI...');
        reinitialize();
      }
    }
  }, [providers, isInitialized, availableProviders, reinitialize]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isInitialized) {
        shutdown();
      }
    };
  }, [isInitialized, shutdown]);

  return {
    // State
    isInitialized,
    isLoading,
    error,
    availableProviders,

    // Actions
    initialize,
    reinitialize,
    shutdown,
    clearError,

    // Messaging
    sendMessage,

    // Utilities
    isReady,
    getStatus
  };
}

/**
 * UAUI Status Card Component
 * Shows UAUI integration status and controls
 */

"use client";

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Sparkles,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Zap,
  Brain,
  Activity,
  Settings,
  Info
} from 'lucide-react';
import { useUAUI } from '../hooks/use-uaui';
import { useAIProviderStore } from '@/stores/ai-provider-store';

export default function UAUIStatusCard() {
  const {
    isInitialized,
    isLoading,
    error,
    availableProviders,
    initialize,
    reinitialize,
    clearError
  } = useUAUI();

  const { providers } = useAIProviderStore();
  const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];

  const getStatusColor = () => {
    if (error) return 'destructive';
    if (isInitialized) return 'success';
    if (isLoading) return 'secondary';
    return 'outline';
  };

  const getStatusText = () => {
    if (error) return 'Error';
    if (isInitialized) return 'Active';
    if (isLoading) return 'Initializing';
    return 'Inactive';
  };

  const getStatusIcon = () => {
    if (error) return AlertCircle;
    if (isInitialized) return CheckCircle;
    if (isLoading) return RefreshCw;
    return Activity;
  };

  const StatusIcon = getStatusIcon();

  return (
    <Card className="card-enhanced shadow-lg">
      <CardHeader className="bg-gradient-to-r from-card to-muted/20 border-b border-border/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Sparkles className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle className="text-lg font-bold bg-gradient-to-r from-foreground to-primary bg-clip-text text-transparent">
                UAUI System Status
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Universal AI User Interface Integration
              </p>
            </div>
          </div>
          <Badge variant={getStatusColor()} className="gap-1">
            <StatusIcon className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
            {getStatusText()}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          {/* System Status */}
          <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
            <Brain className="h-8 w-8 text-primary" />
            <div>
              <p className="font-semibold text-sm">System</p>
              <p className="text-xs text-muted-foreground">
                {isInitialized ? 'Running' : 'Stopped'}
              </p>
            </div>
          </div>

          {/* Active Providers */}
          <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
            <Zap className="h-8 w-8 text-green-500" />
            <div>
              <p className="font-semibold text-sm">Providers</p>
              <p className="text-xs text-muted-foreground">
                {activeProviders.length} active
              </p>
            </div>
          </div>

          {/* Available Models */}
          <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/50">
            <Settings className="h-8 w-8 text-blue-500" />
            <div>
              <p className="font-semibold text-sm">Models</p>
              <p className="text-xs text-muted-foreground">
                {availableProviders.length} available
              </p>
            </div>
          </div>
        </div>

        {/* Status Messages */}
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        )}

        {!isInitialized && !error && activeProviders.length === 0 && (
          <Alert className="mb-4">
            <Info className="h-4 w-4" />
            <AlertDescription>
              Configure AI providers to start using the UAUI system.
            </AlertDescription>
          </Alert>
        )}

        {!isInitialized && !error && activeProviders.length > 0 && (
          <Alert className="mb-4">
            <Info className="h-4 w-4" />
            <AlertDescription>
              AI providers are configured. Initialize UAUI to start using the system.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>

      <CardFooter className="bg-muted/20 border-t border-border/50">
        <div className="flex items-center justify-between w-full">
          <div className="text-xs text-muted-foreground">
            Last updated: {new Date().toLocaleTimeString()}
          </div>
          <div className="flex items-center gap-2">
            {error && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearError}
                className="gap-2"
              >
                <AlertCircle className="h-3 w-3" />
                Clear Error
              </Button>
            )}
            <Button
              variant={isInitialized ? "outline" : "default"}
              size="sm"
              onClick={isInitialized ? reinitialize : initialize}
              disabled={isLoading}
              className="gap-2"
            >
              {isLoading ? (
                <RefreshCw className="h-3 w-3 animate-spin" />
              ) : (
                <Sparkles className="h-3 w-3" />
              )}
              {isInitialized ? 'Reinitialize' : 'Initialize'}
            </Button>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}

{"version": 3, "file": "factory.d.ts", "sourceRoot": "", "sources": ["../src/factory.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAC;AAClC,OAAO,EAAc,UAAU,EAAE,aAAa,EAAE,MAAM,SAAS,CAAC;AAGhE,qBAAa,WAAW;IACtB;;OAEG;IACH,MAAM,CAAC,MAAM,CAAC,OAAO,GAAE,kBAAuB,GAAG,QAAQ;IAKzD;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,OAAO,EAAE,oBAAoB,GAAG,QAAQ;IAyB/D;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,OAAO,EAAE,uBAAuB,GAAG,QAAQ;IAyBrE;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,SAAS,EAAE,sBAAsB,EAAE,GAAG,UAAU,EAAE;IAmC3E,OAAO,CAAC,MAAM,CAAC,WAAW;IAmB1B,OAAO,CAAC,MAAM,CAAC,gBAAgB;IAY/B,OAAO,CAAC,MAAM,CAAC,uBAAuB;CA+BvC;AAMD,MAAM,WAAW,kBAAkB;IACjC,WAAW,CAAC,EAAE,aAAa,GAAG,SAAS,GAAG,YAAY,CAAC;IACvD,QAAQ,CAAC,EAAE,OAAO,GAAG,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC;IAC/C,WAAW,CAAC,EAAE,UAAU,EAAE,CAAC;IAC3B,iBAAiB,CAAC,EAAE,aAAa,GAAG,eAAe,GAAG,OAAO,GAAG,QAAQ,CAAC;IACzE,IAAI,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;IACrC,SAAS,CAAC,EAAE,GAAG,CAAC;IAChB,QAAQ,CAAC,EAAE,GAAG,CAAC;IACf,UAAU,CAAC,EAAE,GAAG,CAAC;CAClB;AAED,MAAM,WAAW,oBAAqB,SAAQ,kBAAkB;IAC9D,YAAY,CAAC,EAAE;QACb,UAAU,CAAC,EAAE,GAAG,CAAC;QACjB,QAAQ,CAAC,EAAE,GAAG,CAAC;QACf,UAAU,CAAC,EAAE,GAAG,CAAC;KAClB,CAAC;CACH;AAED,MAAM,WAAW,uBAAwB,SAAQ,kBAAkB;IACjE,eAAe,CAAC,EAAE;QAChB,SAAS,CAAC,EAAE,GAAG,CAAC;QAChB,aAAa,CAAC,EAAE,GAAG,CAAC;KACrB,CAAC;CACH;AAED,MAAM,WAAW,sBAAsB;IACrC,EAAE,CAAC,EAAE,MAAM,CAAC;IACZ,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,IAAI,EAAE,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,GAAG,MAAM,GAAG,YAAY,CAAC;IACzE,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,MAAM,CAAC,EAAE,MAAM,EAAE,CAAC;IAClB,YAAY,CAAC,EAAE,MAAM,CAAC;IACtB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAMD;;GAEG;AACH,wBAAgB,gBAAgB,CAAC,SAAS,EAAE,sBAAsB,EAAE,GAAG,QAAQ,CAG9E;AAED;;GAEG;AACH,wBAAgB,mBAAmB,CAAC,SAAS,EAAE,sBAAsB,EAAE,GAAG,QAAQ,CAGjF;AAED;;GAEG;AACH,wBAAgB,sBAAsB,CACpC,SAAS,EAAE,sBAAsB,EAAE,EACnC,OAAO,GAAE,kBAAuB,GAC/B,QAAQ,CAGV"}
/**
 * UAUI Protocol - Standalone Implementation
 * Universal AI User Interface Protocol
 */
import { UAUIConfig, UAUIRequest, UAUIResponse, ExistingProviderConfig } from './types';
export declare class UAUI {
    private config;
    private logger;
    private providers;
    private selector;
    private isInitialized;
    constructor(config: UAUIConfig);
    initialize(): Promise<void>;
    processAIRequest(request: UAUIRequest): Promise<UAUIResponse>;
    getAvailableProviders(): Promise<string[]>;
    shutdown(): Promise<void>;
    private createAdapter;
}
export declare function createUAUIFromExisting(providers: ExistingProviderConfig[], options?: {
    environment?: string;
    logLevel?: string;
    selectionStrategy?: string;
}): UAUI;
//# sourceMappingURL=uaui.d.ts.map
{"name": "uaui-protocol", "version": "1.0.0", "description": "Universal AI User Interface Protocol - Intelligent AI provider management system", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build", "setup": "node scripts/setup.js", "demo": "node scripts/demo.js"}, "keywords": ["ai", "artificial-intelligence", "provider-management", "openai", "groq", "openrouter", "claude", "gemini", "mistral", "smart-selection", "cross-app", "protocol", "typescript", "open-source"], "author": "UAUI Protocol Contributors", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/uaui-protocol/uaui.git"}, "bugs": {"url": "https://github.com/uaui-protocol/uaui/issues"}, "homepage": "https://uaui-protocol.dev", "files": ["dist", "README.md", "LICENSE"], "dependencies": {}, "devDependencies": {"@types/node": "^20.0.0", "@types/jest": "^29.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "rimraf": "^5.0.0", "ts-jest": "^29.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=16.0.0"}, "publishConfig": {"access": "public"}}
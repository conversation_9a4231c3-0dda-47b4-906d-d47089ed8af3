/**
 * Admin Navigation Configuration
 * Defines all navigation items, routes, and permissions
 */

import {
  LayoutDashboard,
  MessageSquare,
  Bot,
  Database,
  FileText,
  Settings,
  Users,
  BarChart3,
  Code,
  Palette,
  Zap,
  Brain,
  Globe,
  Shield,
  HelpCircle,
  Sparkles,
  type LucideIcon,
  Beaker
} from 'lucide-react';

export interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon: LucideIcon;
  description?: string;
  badge?: string | number;
  children?: NavigationItem[];
  permissions?: string[];
  isNew?: boolean;
  isComingSoon?: boolean;
}

export interface NavigationSection {
  id: string;
  label: string;
  items: NavigationItem[];
}

export const navigationConfig: NavigationSection[] = [
  // Main Dashboard
  {
    id: 'main',
    label: 'Dashboard',
    items: [
      {
        id: 'overview',
        label: 'Overview',
        href: '/dashboard',
        icon: LayoutDashboard,
        description: 'Dashboard overview and analytics'
      },
      {
        id: 'analytics',
        label: 'Analytics',
        href: '/dashboard/analytics',
        icon: BarChart3,
        description: 'Detailed analytics and reports'
      }
    ]
  },

  // Widget Management
  {
    id: 'widgets',
    label: 'Widget Management',
    items: [
      {
        id: 'widgets',
        label: 'All Widgets',
        href: '/dashboard/widgets',
        icon: MessageSquare,
        description: 'Manage all your chat widgets'
      },
      {
        id: 'widget-builder',
        label: 'Widget Builder',
        href: '/dashboard/widget-builder',
        icon: Palette,
        description: 'Create and customize widgets',
        isNew: true
      },
      {
        id: 'widget-embed',
        label: 'Embed Codes',
        href: '/dashboard/widget-embed',
        icon: Code,
        description: 'Get embed codes for your widgets'
      },
      {
        id: 'widget-templates',
        label: 'Templates',
        href: '/dashboard/widget-templates',
        icon: FileText,
        description: 'Pre-built widget templates',
        isComingSoon: true
      }
    ]
  },

  // AI & Intelligence
  {
    id: 'ai',
    label: 'AI & Intelligence',
    items: [
      {
        id: 'ai-providers',
        label: 'AI Providers',
        href: '/dashboard/ai-providers',
        icon: Bot,
        description: 'Manage AI provider configurations'
      },
      {
        id: 'uaui-system',
        label: 'UAUI Protocol',
        href: '/dashboard/uaui-system',
        icon: Sparkles,
        description: 'Universal AI interface management',
        isNew: true,
        children: [
          {
            id: 'uaui-test',
            label: 'UAUI Test Console',
            href: '/dashboard/uaui-system/test',
            icon: Beaker,
            description: 'Test UAUI functionality',
            isNew: true
          }
        ]
      },
      {
        id: 'prompt-templates',
        label: 'Prompt Templates',
        href: '/dashboard/prompt-templates',
        icon: Brain,
        description: 'Manage AI prompt templates'
      },
      {
        id: 'knowledge-base',
        label: 'Knowledge Base',
        href: '/dashboard/knowledge-base',
        icon: Database,
        description: 'Upload and manage knowledge files'
      },
      {
        id: 'ai-training',
        label: 'AI Training',
        href: '/dashboard/ai-training',
        icon: Zap,
        description: 'Train and fine-tune AI models',
        isComingSoon: true
      }
    ]
  },

  // Content & Communication
  {
    id: 'content',
    label: 'Content & Communication',
    items: [
      {
        id: 'conversations',
        label: 'Conversations',
        href: '/dashboard/conversations',
        icon: MessageSquare,
        description: 'View and manage chat conversations'
      },
      {
        id: 'users',
        label: 'Users',
        href: '/dashboard/users',
        icon: Users,
        description: 'Manage users and permissions'
      },
      {
        id: 'broadcasts',
        label: 'Broadcasts',
        href: '/dashboard/broadcasts',
        icon: Globe,
        description: 'Send messages to multiple users',
        isComingSoon: true
      }
    ]
  },

  // System & Settings
  {
    id: 'system',
    label: 'System & Settings',
    items: [
      {
        id: 'settings',
        label: 'General Settings',
        href: '/dashboard/settings',
        icon: Settings,
        description: 'General application settings'
      },
      {
        id: 'security',
        label: 'Security',
        href: '/dashboard/security',
        icon: Shield,
        description: 'Security and access control'
      },
      {
        id: 'integrations',
        label: 'Integrations',
        href: '/dashboard/integrations',
        icon: Zap,
        description: 'Third-party integrations'
      },
      {
        id: 'api-keys',
        label: 'API Keys',
        href: '/dashboard/api-keys',
        icon: Code,
        description: 'Manage API keys and tokens'
      }
    ]
  },

  // Help & Support
  {
    id: 'support',
    label: 'Help & Support',
    items: [
      {
        id: 'documentation',
        label: 'Documentation',
        href: '/dashboard/documentation',
        icon: FileText,
        description: 'User guides and documentation'
      },
      {
        id: 'support',
        label: 'Support',
        href: '/dashboard/support',
        icon: HelpCircle,
        description: 'Get help and contact support'
      }
    ]
  }
];

/**
 * Get navigation item by ID
 */
export function getNavigationItem(id: string): NavigationItem | null {
  for (const section of navigationConfig) {
    for (const item of section.items) {
      if (item.id === id) return item;
      if (item.children) {
        const child = item.children.find(child => child.id === id);
        if (child) return child;
      }
    }
  }
  return null;
}

/**
 * Get navigation section by ID
 */
export function getNavigationSection(id: string): NavigationSection | null {
  return navigationConfig.find(section => section.id === id) || null;
}

/**
 * Get all navigation items as flat array
 */
export function getAllNavigationItems(): NavigationItem[] {
  const items: NavigationItem[] = [];

  for (const section of navigationConfig) {
    for (const item of section.items) {
      items.push(item);
      if (item.children) {
        items.push(...item.children);
      }
    }
  }

  return items;
}

/**
 * Check if user has permission for navigation item
 */
export function hasPermission(item: NavigationItem, userPermissions: string[] = []): boolean {
  if (!item.permissions || item.permissions.length === 0) return true;
  return item.permissions.some(permission => userPermissions.includes(permission));
}

/**
 * Filter navigation by permissions
 */
export function filterNavigationByPermissions(
  sections: NavigationSection[],
  userPermissions: string[] = []
): NavigationSection[] {
  return sections.map(section => ({
    ...section,
    items: section.items.filter(item => hasPermission(item, userPermissions))
  })).filter(section => section.items.length > 0);
}

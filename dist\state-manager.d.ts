/**
 * UAUI State Manager
 * Handles cross-app state synchronization and persistence
 */
import { Logger } from './utils/logger';
export interface StateSnapshot {
    id: string;
    appId: string;
    timestamp: number;
    state: any;
    version: number;
    checksum?: string;
}
export interface StateSyncOptions {
    immediate?: boolean;
    persistent?: boolean;
    selective?: string[];
    transform?: (state: any) => any;
}
export declare class StateManager {
    private logger;
    private appStates;
    private stateHistory;
    private syncSubscriptions;
    private maxHistorySize;
    private isInitialized;
    constructor(logger: Logger);
    initialize(): Promise<void>;
    /**
     * Set state for an application
     */
    setState(appId: string, state: any, options?: StateSyncOptions): Promise<void>;
    /**
     * Get current state for an application
     */
    getState(appId: string): any;
    /**
     * Get state history for an application
     */
    getStateHistory(appId: string, limit?: number): StateSnapshot[];
    /**
     * Synchronize state between applications
     */
    sync(fromApp: string, toApp: string, state?: any, options?: StateSyncOptions): Promise<void>;
    /**
     * Subscribe to state changes for an application
     */
    subscribe(appId: string, callback: StateSyncCallback): () => void;
    /**
     * Merge states from multiple applications
     */
    mergeStates(appIds: string[], strategy?: 'latest' | 'deep_merge' | 'custom'): Promise<any>;
    /**
     * Restore state from a specific snapshot
     */
    restoreState(appId: string, snapshotId: string): Promise<void>;
    /**
     * Clear state for an application
     */
    clearState(appId: string): Promise<void>;
    /**
     * Get all application states
     */
    getAllStates(): Record<string, any>;
    /**
     * Shutdown the state manager
     */
    shutdown(): Promise<void>;
    private createSnapshot;
    private addToHistory;
    private notifySubscribers;
    private selectiveSync;
    private getLatestSnapshot;
    private mergeByLatest;
    private deepMergeStates;
    private deepMerge;
    private deepClone;
    private calculateChecksum;
}
export interface StateSyncCallback {
    (event: StateChangeEvent): void | Promise<void>;
}
export interface StateChangeEvent {
    appId: string;
    newState: any;
    previousState: any;
    timestamp: number;
}
//# sourceMappingURL=state-manager.d.ts.map
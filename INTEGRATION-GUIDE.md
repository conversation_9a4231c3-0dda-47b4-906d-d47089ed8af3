# UAUI Protocol Integration Guide

The Universal AI User Interface (UAUI) protocol provides a unified way to integrate multiple AI providers into your application. This guide will walk you through the process of integrating UAUI into your application.

## Quick Start

1. **Import the necessary components**:
   ```typescript
   import { 
     UAUI, 
     createUAUIFromExisting, 
     UAUIRequest, 
     UAUIResponse 
   } from 'uaui-system';
   ```

2. **Define your AI providers**:
   ```typescript
   const providers = [
     {
       id: 'openai-1',
       name: 'OpenAI GPT-4',
       type: 'openai',
       apiKey: 'your-openai-api-key',
       defaultModel: 'gpt-4',
       maxTokens: 4000,
       temperature: 0.7,
       timeout: 30000
     },
     {
       id: 'claude-1',
       name: '<PERSON><PERSON><PERSON> <PERSON>',
       type: 'claude',
       apiKey: 'your-claude-api-key',
       defaultModel: 'claude-3-opus-20240229',
       maxTokens: 4000,
       temperature: 0.7,
       timeout: 30000
     }
   ];
   ```

3. **Create a UAUI instance**:
   ```typescript
   const uaui = createUAUIFromExisting(providers, {
     environment: 'development',
     logLevel: 'info',
     selectionStrategy: 'smart'
   });
   ```

4. **Initialize the UAUI instance**:
   ```typescript
   await uaui.initialize();
   ```

5. **Process AI requests**:
   ```typescript
   const request: UAUIRequest = {
     id: 'request-123',
     type: 'ai.request',
     message: 'Hello, how can you help me?',
     context: {
       app: 'my-app',
       userId: 'user-123',
       sessionId: 'session-456',
       timestamp: Date.now()
     }
   };

   const response = await uaui.processAIRequest(request);
   console.log(response.message);
   ```

## React Integration

1. **Create an integration service**:
   ```typescript
   // services/uaui-integration-service.ts
   import { UAUI, createUAUIFromExisting } from 'uaui-system';

   class UAUIIntegrationService {
     private uaui: UAUI | null = null;
     private isInitialized = false;

     async initialize(providers) {
       this.uaui = createUAUIFromExisting(providers, {
         environment: 'development',
         logLevel: 'info',
         selectionStrategy: 'smart'
       });
       await this.uaui.initialize();
       this.isInitialized = true;
       return true;
     }

     async processMessage(message, context) {
       if (!this.isInitialized) {
         throw new Error('UAUI not initialized');
       }
       const request = {
         id: `msg-${Date.now()}`,
         type: 'ai.request',
         message,
         context
       };
       return await this.uaui.processAIRequest(request);
     }
   }

   export const uauiService = new UAUIIntegrationService();
   ```

2. **Create a React hook**:
   ```typescript
   // hooks/use-uaui.ts
   import { useState, useEffect } from 'react';
   import { uauiService } from '../services/uaui-integration-service';

   export function useUAUI(options = {}) {
     const [isInitialized, setIsInitialized] = useState(false);
     const [isLoading, setIsLoading] = useState(false);
     const [error, setError] = useState(null);

     const initialize = async (providers) => {
       try {
         setIsLoading(true);
         const success = await uauiService.initialize(providers);
         setIsInitialized(success);
         return success;
       } catch (err) {
         setError(err.message);
         return false;
       } finally {
         setIsLoading(false);
       }
     };

     const processMessage = async (message, context) => {
       try {
         return await uauiService.processMessage(message, context);
       } catch (err) {
         setError(err.message);
         return null;
       }
     };

     return {
       isInitialized,
       isLoading,
       error,
       initialize,
       processMessage
     };
   }
   ```

3. **Use the hook in your components**:
   ```tsx
   import { useUAUI } from '../hooks/use-uaui';

   function AIChat() {
     const { isInitialized, isLoading, error, initialize, processMessage } = useUAUI();
     const [messages, setMessages] = useState([]);
     const [input, setInput] = useState('');

     useEffect(() => {
       // Initialize with your providers
       initialize([{ id: 'openai-1', name: 'OpenAI', /* ... */ }]);
     }, []);

     const sendMessage = async () => {
       const response = await processMessage(input, { userId: 'user-123' });
       setMessages([...messages, { user: input }, { ai: response.message }]);
       setInput('');
     };

     return (
       <div>
         {messages.map((msg, i) => (
           <div key={i}>
             {msg.user && <div>User: {msg.user}</div>}
             {msg.ai && <div>AI: {msg.ai}</div>}
           </div>
         ))}
         <input value={input} onChange={e => setInput(e.target.value)} />
         <button onClick={sendMessage} disabled={!isInitialized || isLoading}>Send</button>
       </div>
     );
   }
   ```

## Advanced Configuration

### Selection Strategies

UAUI supports different strategies for selecting which AI provider to use:

- **smart**: Intelligently selects the best provider based on the content of the request
- **round_robin**: Rotates through providers to balance load
- **least_latency**: Selects the provider with the lowest response time

```typescript
const uaui = createUAUIFromExisting(providers, {
  selectionStrategy: 'smart' // or 'round_robin' or 'least_latency'
});
```

### Action Handling

UAUI can generate actions that your application can execute:

```typescript
const response = await uaui.processAIRequest(request);

// Handle actions
if (response.actions && response.actions.length > 0) {
  response.actions.forEach(action => {
    switch (action.type) {
      case 'navigation':
        // Handle navigation action
        break;
      case 'ui.update':
        // Update UI
        break;
      // ...other action types
    }
  });
}
```

## Troubleshooting

### Common Issues

1. **Initialization Failures**:
   - Ensure you have valid API keys for your providers
   - Check that your providers array is properly formatted
   - Verify network connectivity

2. **Provider Not Responding**:
   - Check API key validity
   - Verify provider service status
   - Ensure request format is correct

3. **TypeScript Errors**:
   - Import types directly from UAUI system
   - Ensure your TypeScript configuration targets ES2020 or higher

### Debugging

Enable debug logging by setting the log level to 'debug':

```typescript
const uaui = createUAUIFromExisting(providers, {
  logLevel: 'debug'
});
```

## API Reference

For a complete API reference, see the [API Documentation](./API.md).

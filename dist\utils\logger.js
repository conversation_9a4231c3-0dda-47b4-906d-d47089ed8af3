"use strict";
/**
 * Simple Logger Utility for UAUI Protocol
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.Logger = void 0;
exports.createLogger = createLogger;
class Logger {
    constructor(config = { level: 'info' }) {
        this.config = {
            timestamp: true,
            ...config
        };
    }
    shouldLog(level) {
        return Logger.levels[level] >= Logger.levels[this.config.level];
    }
    formatMessage(level, message, ...args) {
        const timestamp = this.config.timestamp ? `[${new Date().toISOString()}]` : '';
        const prefix = this.config.prefix ? `[${this.config.prefix}]` : '';
        const levelStr = `[${level.toUpperCase()}]`;
        return `${timestamp}${prefix}${levelStr} ${message}`;
    }
    debug(message, ...args) {
        if (this.shouldLog('debug')) {
            console.debug(this.formatMessage('debug', message), ...args);
        }
    }
    info(message, ...args) {
        if (this.shouldLog('info')) {
            console.info(this.formatMessage('info', message), ...args);
        }
    }
    warn(message, ...args) {
        if (this.shouldLog('warn')) {
            console.warn(this.formatMessage('warn', message), ...args);
        }
    }
    error(message, ...args) {
        if (this.shouldLog('error')) {
            console.error(this.formatMessage('error', message), ...args);
        }
    }
    setLevel(level) {
        this.config.level = level;
    }
    getLevel() {
        return this.config.level;
    }
}
exports.Logger = Logger;
Logger.levels = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3
};
// Default logger instance
exports.logger = new Logger();
// Factory function for creating loggers
function createLogger(config) {
    return new Logger(config);
}
//# sourceMappingURL=logger.js.map
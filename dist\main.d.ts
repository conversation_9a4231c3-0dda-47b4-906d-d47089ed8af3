/**
 * UAUI Protocol - Universal AI User Interface
 * Standalone Open Source AI Provider Management System
 */
export { UAUI, createUAUIFromExisting } from './uaui';
export type { UAUIConfig, UAUIRequest, UAUIResponse, UAUIAction, AIProvider, AIProviderType, AIProviderConfig, RequestContext, ResponseMetadata, UAUIError, ExistingProviderConfig, TempoWidgetConfig, TempoAIProvider, TempoAPIClient } from './types';
export declare const VERSION = "1.0.0";
export declare const PROTOCOL_VERSION = "1.0";
export declare function getVersion(): string;
export declare function getProtocolVersion(): string;
export declare function isCompatible(version: string): boolean;
//# sourceMappingURL=main.d.ts.map
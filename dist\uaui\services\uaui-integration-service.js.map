{"version": 3, "file": "uaui-integration-service.js", "sourceRoot": "", "sources": ["../../../src/uaui/services/uaui-integration-service.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,6BAAqD;AACrD,kEAAgE;AAuBhE;;;GAGG;AACH,MAAM,sBAAsB;IAK1B,YAAY,SAA0B;QACpC,WAAW,EAAE,aAAa;QAC1B,QAAQ,EAAE,MAAM;QAChB,iBAAiB,EAAE,OAAO;KAC3B;QARO,SAAI,GAAgB,IAAI,CAAC;QACzB,kBAAa,GAAG,KAAK,CAAC;QAQ5B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,2CAA2C;YAC3C,MAAM,EAAE,SAAS,EAAE,GAAG,sCAAkB,CAAC,QAAQ,EAAE,CAAC;YAEpD,yCAAyC;YACzC,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAE5G,IAAI,eAAe,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjC,OAAO,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC;gBACrE,OAAO,KAAK,CAAC;YACf,CAAC;YAED,yCAAyC;YACzC,MAAM,aAAa,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACrD,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,MAAM,EAAE,QAAQ,CAAC,MAAM,IAAI,EAAE;gBAC7B,YAAY,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,IAAI,SAAS;gBAC/C,SAAS,EAAE,IAAI;gBACf,WAAW,EAAE,GAAG;gBAChB,OAAO,EAAE,KAAK;aACf,CAAC,CAAC,CAAC;YAEJ,OAAO,CAAC,GAAG,CAAC,iBAAiB,aAAa,CAAC,MAAM,iCAAiC,CAAC,CAAC;YAEpF,uBAAuB;YACvB,IAAI,CAAC,IAAI,GAAG,IAAA,0BAAsB,EAAC,aAAa,EAAE;gBAChD,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,aAAa;gBACrD,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,IAAI,MAAM;gBACxC,iBAAiB,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,OAAO;aAC5D,CAAC,CAAC;YAEH,kBAAkB;YAClB,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAE1B,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE,KAAK,CAAC,CAAC;YAC1E,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YAC3B,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAE7D,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,QAAQ;QACZ,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAC/D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,OAAoB;QACpC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACtC,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC;gBAChD,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;gBAC9B,QAAQ,EAAE;oBACR,MAAM,EAAE,cAAc;oBACtB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,GAAG,OAAO,CAAC,QAAQ;iBACpB;aACF,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO,EAAE,CAAC;QAE1B,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC7D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,OAAO;QACL,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC;IAClD,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO;YACL,WAAW,EAAE,IAAI,CAAC,aAAa;YAC/B,WAAW,EAAE,IAAI,CAAC,IAAI,KAAK,IAAI;YAC/B,SAAS,EAAE,IAAI,CAAC,qBAAqB,EAAE;YACvC,MAAM,EAAE,IAAI,CAAC,MAAM;SACpB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAmC;QAC9C,IAAI,CAAC,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,SAAS,EAAE,CAAC;IACjD,CAAC;CACF;AAMQ,wDAAsB;AAJ/B,4BAA4B;AAC5B,MAAM,sBAAsB,GAAG,IAAI,sBAAsB,EAAE,CAAC;AAE5D,kBAAe,sBAAsB,CAAC"}
/**
 * Universal AI Service
 * Integrates with multiple AI providers and provides intelligent selection
 */

import {
  AIConfig,
  AIProvider,
  AIProviderType,
  UAUIRequest,
  UAUIResponse,
  RequestContext,
  ResponseMetadata,
  UAUIError
} from './types';
import { Logger } from './utils/logger';

export interface AIServiceResponse {
  message: string;
  actions?: any[];
  data?: any;
  metadata: ResponseMetadata;
}

export class UniversalAIService {
  private config: AIConfig;
  private logger: Logger;
  private providers: Map<string, AIProviderAdapter> = new Map();
  private selectionStrategy: ProviderSelectionStrategy;
  private isInitialized: boolean = false;

  constructor(config: AIConfig, logger: Logger) {
    this.config = config;
    this.logger = logger;
    this.selectionStrategy = this.createSelectionStrategy(config.selectionStrategy);
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.logger.info('Initializing Universal AI Service...');

      // Initialize all configured providers
      for (const provider of this.config.providers) {
        const adapter = this.createProviderAdapter(provider);
        await adapter.initialize();
        this.providers.set(provider.id, adapter);
        this.logger.info(`Initialized AI provider: ${provider.name} (${provider.type})`);
      }

      this.isInitialized = true;
      this.logger.info('Universal AI Service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize AI Service:', error);
      throw error;
    }
  }

  async process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse> {
    if (!this.isInitialized) {
      throw new Error('AI Service not initialized');
    }

    const startTime = Date.now();

    try {
      // Select the best provider for this request
      const provider = await this.selectionStrategy.select(request, context, this.providers);

      this.logger.debug(`Selected provider: ${provider.getId()} for request: ${request.id}`);

      // Process the request
      const response = await provider.process(request, context);

      // Update provider metrics
      await this.updateProviderMetrics(provider.getId(), {
        success: true,
        responseTime: Date.now() - startTime,
        tokens: response.metadata.tokens || 0
      });

      return response;

    } catch (error) {
      this.logger.error('AI processing failed:', error);

      // Try fallback if enabled
      if (this.config.fallbackEnabled) {
        return this.processFallback(request, context, error);
      }

      throw error;
    }
  }

  async getProvider(providerId: string): Promise<AIProviderAdapter | undefined> {
    return this.providers.get(providerId);
  }

  async getAvailableProviders(): Promise<AIProviderAdapter[]> {
    // Return all providers without checking isAvailable
    return Array.from(this.providers.values());
  }

  async testProvider(providerId: string): Promise<boolean> {
    const provider = this.providers.get(providerId);
    if (!provider) return false;

    try {
      return await provider.test();
    } catch (error) {
      this.logger.error(`Provider test failed: ${providerId}`, error);
      return false;
    }
  }

  async shutdown(): Promise<void> {
    this.logger.info('Shutting down Universal AI Service...');

    for (const provider of this.providers.values()) {
      await provider.shutdown();
    }

    this.providers.clear();
    this.isInitialized = false;
  }

  // Private methods

  private createProviderAdapter(provider: AIProvider): AIProviderAdapter {
    switch (provider.type) {
      case 'openai':
        return new OpenAIAdapter(provider, this.logger);
      case 'claude':
        return new ClaudeAdapter(provider, this.logger);
      case 'gemini':
        return new GeminiAdapter(provider, this.logger);
      case 'mistral':
        return new MistralAdapter(provider, this.logger);
      case 'groq':
        return new GroqAdapter(provider, this.logger);
      default:
        throw new Error(`Unsupported provider type: ${provider.type}`);
    }
  }

  private createSelectionStrategy(strategy: string): ProviderSelectionStrategy {
    switch (strategy) {
      case 'smart':
        return new SmartSelectionStrategy(this.logger);
      case 'round_robin':
        return new RoundRobinStrategy(this.logger);
      case 'least_latency':
        return new LeastLatencyStrategy(this.logger);
      default:
        return new SmartSelectionStrategy(this.logger);
    }
  }

  private async processFallback(
    request: UAUIRequest,
    context: RequestContext,
    originalError: any
  ): Promise<AIServiceResponse> {
    this.logger.warn('Attempting fallback processing...');

    // Try with a different provider
    const availableProviders = await this.getAvailableProviders();

    for (const provider of availableProviders) {
      try {
        const response = await provider.process(request, context);
        this.logger.info(`Fallback successful with provider: ${provider.getId()}`);
        return response;
      } catch (fallbackError) {
        this.logger.warn(`Fallback failed with provider: ${provider.getId()}`, fallbackError);
        continue;
      }
    }

    // All fallbacks failed
    throw new Error(`All providers failed. Original error: ${originalError.message}`);
  }

  private async updateProviderMetrics(providerId: string, metrics: any): Promise<void> {
    // Update provider performance metrics
    // This would typically update a database or metrics store
    this.logger.debug(`Updated metrics for provider: ${providerId}`, metrics);
  }
}

// ============================================================================
// PROVIDER ADAPTERS
// ============================================================================

export abstract class AIProviderAdapter {
  protected provider: AIProvider;
  protected logger: Logger;

  constructor(provider: AIProvider, logger: Logger) {
    this.provider = provider;
    this.logger = logger;
  }

  abstract initialize(): Promise<void>;
  abstract process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse>;
  abstract test(): Promise<boolean>;
  abstract isAvailable(): Promise<boolean>;
  abstract shutdown(): Promise<void>;

  getId(): string {
    return this.provider.id;
  }

  getName(): string {
    return this.provider.name;
  }

  getType(): AIProviderType {
    return this.provider.type;
  }

  /**
   * Generate actions from AI response text
   */
  protected generateActionsFromResponse(responseText: string, request: UAUIRequest): any[] {
    const actions: any[] = [];
    const lowerText = responseText.toLowerCase();
    const lowerMessage = (request.message || '').toLowerCase();

    // Color change actions
    if (lowerText.includes('color') || lowerMessage.includes('color')) {
      if (lowerText.includes('blue') || lowerMessage.includes('blue')) {
        actions.push({
          id: `action-${Date.now()}`,
          type: 'widget.appearance.update',
          target: 'widget',
          payload: { primaryColor: '#3b82f6' }
        });
      } else if (lowerText.includes('red') || lowerMessage.includes('red')) {
        actions.push({
          id: `action-${Date.now()}`,
          type: 'widget.appearance.update',
          target: 'widget',
          payload: { primaryColor: '#ef4444' }
        });
      }
    }

    // Navigation actions
    if (lowerText.includes('analytics') || lowerText.includes('dashboard') ||
      lowerMessage.includes('analytics') || lowerMessage.includes('dashboard')) {
      actions.push({
        id: `action-${Date.now()}`,
        type: 'cross_app.navigate',
        target: 'dashboard',
        payload: { view: 'analytics', timeRange: 'last_30_days' }
      });
    }

    // Configuration actions
    if (lowerText.includes('configure') || lowerText.includes('settings') ||
      lowerMessage.includes('configure') || lowerMessage.includes('settings')) {
      actions.push({
        id: `action-${Date.now()}`,
        type: 'ui.update',
        target: 'widget',
        payload: { showConfigPanel: true }
      });
    }

    return actions;
  }
}

// OpenAI Adapter
export class OpenAIAdapter extends AIProviderAdapter {
  private client: any; // OpenAI client

  async initialize(): Promise<void> {
    // Initialize OpenAI client
    this.logger.debug(`Initializing OpenAI adapter: ${this.provider.id}`);
  }

  async process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse> {
    const startTime = Date.now();

    // Process with OpenAI
    const response = await this.callOpenAI(request, context);

    return {
      message: response.message,
      actions: response.actions,
      data: response.data,
      metadata: {
        provider: this.provider.name,
        model: this.provider.config.defaultModel,
        tokens: response.tokens,
        responseTime: Date.now() - startTime,
        timestamp: Date.now()
      }
    };
  }

  async test(): Promise<boolean> {
    try {
      // Test OpenAI connection with a simple request
      const apiKey = this.provider.config.apiKey;
      if (!apiKey) return false;

      const response = await fetch('https://api.openai.com/v1/models', {
        headers: { 'Authorization': `Bearer ${apiKey}` }
      });

      return response.ok;
    } catch {
      return false;
    }
  }

  async isAvailable(): Promise<boolean> {
    return this.provider.status.available;
  }

  async shutdown(): Promise<void> {
    // Cleanup OpenAI client
  }

  private async callOpenAI(request: UAUIRequest, context: RequestContext): Promise<any> {
    const apiKey = this.provider.config.apiKey;
    if (!apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), this.provider.config.timeout || 30000);

    try {
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'User-Agent': 'UAUI/1.0.0'
        },
        body: JSON.stringify({
          model: this.provider.config.defaultModel || 'gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful AI assistant integrated into a widget system. Provide clear, actionable responses.'
            },
            {
              role: 'user',
              content: request.message || ''
            }
          ],
          max_tokens: this.provider.config.maxTokens || 4000,
          temperature: this.provider.config.temperature || 0.7,
          stream: false
        }),
        signal: controller.signal
      });

      clearTimeout(timeout);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})) as any;
        throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json() as any;
      const responseText = data.choices?.[0]?.message?.content || 'No response received';

      return {
        message: responseText,
        tokens: data.usage?.total_tokens || 0,
        actions: this.generateActionsFromResponse(responseText, request)
      };
    } catch (error: any) {
      clearTimeout(timeout);
      if (error.name === 'AbortError') {
        throw new Error('OpenAI request timeout');
      }
      throw error;
    }
  }
}

// Claude Adapter
export class ClaudeAdapter extends AIProviderAdapter {
  async initialize(): Promise<void> {
    this.logger.debug(`Initializing Claude adapter: ${this.provider.id}`);
  }

  async process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse> {
    const startTime = Date.now();

    const response = await this.callClaude(request, context);

    return {
      message: response.message,
      actions: response.actions,
      data: response.data,
      metadata: {
        provider: this.provider.name,
        model: this.provider.config.defaultModel,
        tokens: response.tokens,
        responseTime: Date.now() - startTime,
        timestamp: Date.now()
      }
    };
  }

  async test(): Promise<boolean> {
    return true;
  }

  async isAvailable(): Promise<boolean> {
    return this.provider.status.available;
  }

  async shutdown(): Promise<void> {
    // Cleanup
  }

  private async callClaude(request: UAUIRequest, context: RequestContext): Promise<any> {
    return {
      message: `Claude response to: ${request.message}`,
      tokens: 120,
      actions: []
    };
  }
}

// Similar adapters for Gemini, Mistral, Groq...
export class GeminiAdapter extends AIProviderAdapter {
  async initialize(): Promise<void> {
    this.logger.debug(`Initializing Gemini adapter: ${this.provider.id}`);
  }

  async process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse> {
    const startTime = Date.now();
    const response = await this.callGemini(request, context);

    return {
      message: response.message,
      actions: response.actions,
      data: response.data,
      metadata: {
        provider: this.provider.name,
        model: this.provider.config.defaultModel,
        tokens: response.tokens,
        responseTime: Date.now() - startTime,
        timestamp: Date.now()
      }
    };
  }

  async test(): Promise<boolean> { return true; }
  async isAvailable(): Promise<boolean> { return this.provider.status.available; }
  async shutdown(): Promise<void> { /* Cleanup */ }

  private async callGemini(request: UAUIRequest, context: RequestContext): Promise<any> {
    return {
      message: `Gemini response to: ${request.message}`,
      tokens: 90,
      actions: []
    };
  }
}

export class MistralAdapter extends AIProviderAdapter {
  async initialize(): Promise<void> {
    this.logger.debug(`Initializing Mistral adapter: ${this.provider.id}`);
  }

  async process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse> {
    const startTime = Date.now();
    const response = await this.callMistral(request, context);

    return {
      message: response.message,
      actions: response.actions,
      data: response.data,
      metadata: {
        provider: this.provider.name,
        model: this.provider.config.defaultModel,
        tokens: response.tokens,
        responseTime: Date.now() - startTime,
        timestamp: Date.now()
      }
    };
  }

  async test(): Promise<boolean> { return true; }
  async isAvailable(): Promise<boolean> { return this.provider.status.available; }
  async shutdown(): Promise<void> { /* Cleanup */ }

  private async callMistral(request: UAUIRequest, context: RequestContext): Promise<any> {
    return {
      message: `Mistral response to: ${request.message}`,
      tokens: 85,
      actions: []
    };
  }
}

export class GroqAdapter extends AIProviderAdapter {
  async initialize(): Promise<void> {
    this.logger.debug(`Initializing Groq adapter: ${this.provider.id}`);
  }

  async process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse> {
    const startTime = Date.now();
    const response = await this.callGroq(request, context);

    return {
      message: response.message,
      actions: response.actions,
      data: response.data,
      metadata: {
        provider: this.provider.name,
        model: this.provider.config.defaultModel,
        tokens: response.tokens,
        responseTime: Date.now() - startTime,
        timestamp: Date.now()
      }
    };
  }

  async test(): Promise<boolean> {
    try {
      const apiKey = this.provider.config.apiKey;
      if (!apiKey) return false;

      const response = await fetch('https://api.groq.com/openai/v1/models', {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return response.ok;
    } catch {
      return false;
    }
  }

  async isAvailable(): Promise<boolean> {
    return this.provider.status.available;
  }

  async shutdown(): Promise<void> {
    // Cleanup Groq resources
  }

  private async callGroq(request: UAUIRequest, context: RequestContext): Promise<any> {
    const apiKey = this.provider.config.apiKey;
    if (!apiKey) {
      throw new Error('Groq API key not configured');
    }

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), this.provider.config.timeout || 30000);

    try {
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'User-Agent': 'UAUI/1.0.0'
        },
        body: JSON.stringify({
          model: this.provider.config.defaultModel || 'llama3-70b-8192',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful AI assistant integrated into a widget system. Provide clear, actionable responses quickly.'
            },
            {
              role: 'user',
              content: request.message || ''
            }
          ],
          max_tokens: this.provider.config.maxTokens || 4000,
          temperature: this.provider.config.temperature || 0.7,
          stream: false
        }),
        signal: controller.signal
      });

      clearTimeout(timeout);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})) as any;
        throw new Error(`Groq API error: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json() as any;
      const responseText = data.choices?.[0]?.message?.content || 'No response received';

      return {
        message: responseText,
        tokens: data.usage?.total_tokens || 0,
        actions: this.generateActionsFromResponse(responseText, request)
      };
    } catch (error: any) {
      clearTimeout(timeout);
      if (error.name === 'AbortError') {
        throw new Error('Groq request timeout');
      }
      throw error;
    }
  }
}

// OpenRouter Adapter - Access to multiple models through one API
export class OpenRouterAdapter extends AIProviderAdapter {
  async initialize(): Promise<void> {
    this.logger.debug(`Initializing OpenRouter adapter: ${this.provider.id}`);
  }

  async process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse> {
    const startTime = Date.now();
    const response = await this.callOpenRouter(request, context);

    return {
      message: response.message,
      actions: response.actions,
      data: response.data,
      metadata: {
        provider: this.provider.name,
        model: this.provider.config.defaultModel,
        tokens: response.tokens,
        responseTime: Date.now() - startTime,
        timestamp: Date.now()
      }
    };
  }

  async test(): Promise<boolean> {
    try {
      const apiKey = this.provider.config.apiKey;
      if (!apiKey) return false;

      const response = await fetch('https://openrouter.ai/api/v1/models', {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      return response.ok;
    } catch {
      return false;
    }
  }

  async isAvailable(): Promise<boolean> {
    return this.provider.status.available;
  }

  async shutdown(): Promise<void> {
    // Cleanup OpenRouter resources
  }

  private async callOpenRouter(request: UAUIRequest, context: RequestContext): Promise<any> {
    const apiKey = this.provider.config.apiKey;
    if (!apiKey) {
      throw new Error('OpenRouter API key not configured');
    }

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), this.provider.config.timeout || 30000);

    try {
      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://your-app.com', // Replace with your app URL
          'X-Title': 'UAUI Protocol',
          'User-Agent': 'UAUI/1.0.0'
        },
        body: JSON.stringify({
          model: this.provider.config.defaultModel || 'openai/gpt-4',
          messages: [
            {
              role: 'system',
              content: 'You are a helpful AI assistant integrated into a widget system. Provide clear, actionable responses.'
            },
            {
              role: 'user',
              content: request.message || ''
            }
          ],
          max_tokens: this.provider.config.maxTokens || 4000,
          temperature: this.provider.config.temperature || 0.7,
          stream: false
        }),
        signal: controller.signal
      });

      clearTimeout(timeout);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})) as any;
        throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`);
      }

      const data = await response.json() as any;
      const responseText = data.choices?.[0]?.message?.content || 'No response received';

      return {
        message: responseText,
        tokens: data.usage?.total_tokens || 0,
        actions: this.generateActionsFromResponse(responseText, request)
      };
    } catch (error: any) {
      clearTimeout(timeout);
      if (error.name === 'AbortError') {
        throw new Error('OpenRouter request timeout');
      }
      throw error;
    }
  }
}

// ============================================================================
// PROVIDER SELECTION STRATEGIES
// ============================================================================

export abstract class ProviderSelectionStrategy {
  protected logger: Logger;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  abstract select(
    request: UAUIRequest,
    context: RequestContext,
    providers: Map<string, AIProviderAdapter>
  ): Promise<AIProviderAdapter>;
}

export class SmartSelectionStrategy extends ProviderSelectionStrategy {
  async select(
    request: UAUIRequest,
    context: RequestContext,
    providers: Map<string, AIProviderAdapter>
  ): Promise<AIProviderAdapter> {
    const message = request.message?.toLowerCase() || '';

    // Smart selection based on content and context
    if (message.includes('analyze') || message.includes('insight')) {
      return this.findProvider(providers, 'claude') || this.getFirstAvailable(providers);
    }

    if (message.includes('quick') || message.includes('fast')) {
      return this.findProvider(providers, 'groq') || this.getFirstAvailable(providers);
    }

    if (message.includes('creative') || message.includes('visual')) {
      return this.findProvider(providers, 'gemini') || this.getFirstAvailable(providers);
    }

    if (message.includes('code') || message.includes('technical')) {
      return this.findProvider(providers, 'openai') || this.getFirstAvailable(providers);
    }

    // Default to OpenAI or first available
    return this.findProvider(providers, 'openai') || this.getFirstAvailable(providers);
  }

  private findProvider(providers: Map<string, AIProviderAdapter>, type: string): AIProviderAdapter | null {
    for (const provider of providers.values()) {
      if (provider.getType() === type) {
        return provider;
      }
    }
    return null;
  }

  private getFirstAvailable(providers: Map<string, AIProviderAdapter>): AIProviderAdapter {
    const provider = providers.values().next().value;
    if (!provider) {
      throw new Error('No AI providers available');
    }
    return provider;
  }
}

export class RoundRobinStrategy extends ProviderSelectionStrategy {
  private currentIndex = 0;

  async select(
    request: UAUIRequest,
    context: RequestContext,
    providers: Map<string, AIProviderAdapter>
  ): Promise<AIProviderAdapter> {
    const providerArray = Array.from(providers.values());
    if (providerArray.length === 0) {
      throw new Error('No AI providers available');
    }
    const provider = providerArray[this.currentIndex % providerArray.length];
    this.currentIndex++;
    return provider;
  }
}

export class LeastLatencyStrategy extends ProviderSelectionStrategy {
  private performanceMetrics = new Map<string, { avgLatency: number, requestCount: number }>();

  async select(
    request: UAUIRequest,
    context: RequestContext,
    providers: Map<string, AIProviderAdapter>
  ): Promise<AIProviderAdapter> {
    // Select provider with lowest latency based on historical data
    let bestProvider: AIProviderAdapter | null = null;
    let lowestLatency = Infinity;

    for (const provider of providers.values()) {
      const metrics = this.performanceMetrics.get(provider.getId());
      const avgLatency = metrics?.avgLatency || 500; // Default latency

      if (avgLatency < lowestLatency) {
        lowestLatency = avgLatency;
        bestProvider = provider;
      }
    }

    if (!bestProvider) {
      const provider = providers.values().next().value;
      if (!provider) {
        throw new Error('No AI providers available');
      }
      return provider;
    }

    return bestProvider;
  }

  updateMetrics(providerId: string, latency: number): void {
    const current = this.performanceMetrics.get(providerId) || { avgLatency: 0, requestCount: 0 };
    const newCount = current.requestCount + 1;
    const newAvg = (current.avgLatency * current.requestCount + latency) / newCount;

    this.performanceMetrics.set(providerId, {
      avgLatency: newAvg,
      requestCount: newCount
    });
  }
}

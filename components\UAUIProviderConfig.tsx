/**
 * UAUI Provider Configuration Component
 * Displays sample configurations for different AI providers
 */

"use client";

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/components/ui/use-toast';
import { useAIProviderStore } from '@/stores/ai-provider-store';
import { useUAUI } from '../hooks/use-uaui';
import {
    Bot,
    Copy,
    CheckCircle,
    AlertCircle,
    Info,
    Sparkles,
    ExternalLink,
    Settings
} from 'lucide-react';

interface UAUIProviderConfigProps {
    onConfigureProvider?: (provider: any) => void;
}

export default function UAUIProviderConfig({ onConfigureProvider }: UAUIProviderConfigProps) {
    const { providers, createProvider, updateProvider } = useAIProviderStore();
    const { reinitialize } = useUAUI();
    const { toast } = useToast();

    // Sample provider configurations
    const sampleProviders = [
        {
            name: 'OpenAI GPT-4',
            type: 'openai',
            apiKeyPlaceholder: 'sk-...',
            defaultModel: 'gpt-4',
            models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
            instructions: [
                'Create an OpenAI account at https://platform.openai.com',
                'Navigate to API keys section',
                'Create a new API key',
                'Copy the key and paste it below'
            ]
        },
        {
            name: 'Anthropic Claude',
            type: 'claude',
            apiKeyPlaceholder: 'sk-ant-...',
            defaultModel: 'claude-3-opus-********',
            models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
            instructions: [
                'Create an Anthropic account at https://console.anthropic.com',
                'Navigate to API keys section',
                'Create a new API key',
                'Copy the key and paste it below'
            ]
        },
        {
            name: 'Google Gemini',
            type: 'gemini',
            apiKeyPlaceholder: 'AIza...',
            defaultModel: 'gemini-pro',
            models: ['gemini-pro', 'gemini-ultra'],
            instructions: [
                'Go to Google AI Studio at https://makersuite.google.com',
                'Create a new project or select existing',
                'Generate an API key',
                'Copy the key and paste it below'
            ]
        },
        {
            name: 'Mistral AI',
            type: 'mistral',
            apiKeyPlaceholder: 'mst_...',
            defaultModel: 'mistral-large',
            models: ['mistral-large', 'mistral-medium', 'mistral-small'],
            instructions: [
                'Create a Mistral account at https://console.mistral.ai',
                'Navigate to API keys section',
                'Create a new API key',
                'Copy the key and paste it below'
            ]
        },
        {
            name: 'Groq',
            type: 'groq',
            apiKeyPlaceholder: 'gsk_...',
            defaultModel: 'llama3-70b-8192',
            models: ['llama3-70b-8192', 'llama3-8b-8192', 'mixtral-8x7b-32768'],
            instructions: [
                'Create a Groq account at https://console.groq.com',
                'Navigate to API keys section',
                'Create a new API key',
                'Copy the key and paste it below'
            ]
        }
    ];

    const handleQuickConfigure = async (provider: any) => {
        if (onConfigureProvider) {
            onConfigureProvider(provider);
            return;
        }

        // Check if provider already exists
        const existingProvider = Array.isArray(providers) ? providers.find(p => p.type === provider.type) : undefined;

        if (existingProvider) {
            if (existingProvider.isConfigured) {
                toast({
                    title: "Provider Already Configured",
                    description: `${provider.name} is already set up. You can manage it in the AI Providers page.`,
                });
                return;
            } else {
                toast({
                    title: "Complete Configuration",
                    description: `${provider.name} exists but needs an API key. Redirecting to AI Providers page...`,
                });
                // Redirect to AI providers page after a short delay
                setTimeout(() => {
                    window.location.href = '/dashboard/ai-providers';
                }, 1500);
                return;
            }
        }

        try {
            // Create new provider without API key
            const success = await createProvider({
                name: provider.name,
                type: provider.type as any,
                apiKey: '',
                isActive: true
            });

            if (success) {
                toast({
                    title: "Provider Created",
                    description: `${provider.name} has been added. Now add your API key to complete the setup.`,
                    action: (
                        <Link href="/dashboard/ai-providers">
                            <Button variant="outline" size="sm" className="gap-2">
                                <Settings className="h-3 w-3" />
                                Configure
                            </Button>
                        </Link>
                    ),
                });
                
                // Redirect to AI providers page after a short delay
                setTimeout(() => {
                    window.location.href = '/dashboard/ai-providers';
                }, 2000);
            } else {
                toast({
                    title: "Error",
                    description: "Failed to create provider. Please try again.",
                    variant: "destructive",
                });
            }
        } catch (error) {
            toast({
                title: "Error",
                description: "An unexpected error occurred while creating the provider.",
                variant: "destructive",
            });
        }
    };

    const getProviderStatusBadge = (providerType: string) => {
        const existingProvider = Array.isArray(providers) ? providers.find(p => p.type === providerType) : undefined;

        if (!existingProvider) {
            return (
                <Badge variant="outline" className="text-xs">
                    Not Configured
                </Badge>
            );
        }

        if (!existingProvider.isConfigured) {
            return (
                <Badge variant="secondary" className="text-xs">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    No API Key
                </Badge>
            );
        }

        if (!existingProvider.isActive) {
            return (
                <Badge variant="secondary" className="text-xs">
                    Inactive
                </Badge>
            );
        }

        return (
            <Badge variant="default" className="text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                Active
            </Badge>
        );
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                    <Sparkles className="h-5 w-5 text-primary" />
                    UAUI Provider Configurations
                </CardTitle>
            </CardHeader>
            <CardContent>
                <Alert className="mb-4">
                    <Info className="h-4 w-4" />
                    <AlertDescription>
                        Configure AI providers to test the UAUI system. Click "Setup Provider" to create a provider entry, then you'll be redirected to add your API key.
                    </AlertDescription>
                </Alert>

                <Tabs defaultValue={sampleProviders[0].type}>
                    <TabsList className="w-full">
                        {sampleProviders.map(provider => (
                            <TabsTrigger key={provider.type} value={provider.type} className="flex items-center gap-1">
                                <Bot className="h-4 w-4" />
                                <span>{provider.name.split(' ')[0]}</span>
                                {getProviderStatusBadge(provider.type)}
                            </TabsTrigger>
                        ))}
                    </TabsList>

                    {sampleProviders.map(provider => (
                        <TabsContent key={provider.type} value={provider.type} className="mt-4 space-y-4">
                            <div className="flex justify-between items-start">
                                <div>
                                    <h3 className="text-base font-medium">{provider.name}</h3>
                                    <p className="text-sm text-muted-foreground">Default Model: {provider.defaultModel}</p>
                                </div>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleQuickConfigure(provider)}
                                    className="gap-2"
                                >
                                    <Settings className="h-3 w-3" />
                                    {(() => {
                                        const existingProvider = Array.isArray(providers) ? providers.find(p => p.type === provider.type) : undefined;
                                        if (existingProvider?.isConfigured) {
                                            return "Manage Provider";
                                        } else if (existingProvider) {
                                            return "Add API Key";
                                        } else {
                                            return "Setup Provider";
                                        }
                                    })()}
                                </Button>
                            </div>

                            <Separator />

                            <div>
                                <h4 className="text-sm font-medium mb-2">Setup Instructions:</h4>
                                <ol className="space-y-1 text-sm ml-5 list-decimal">
                                    {provider.instructions.map((instruction, idx) => (
                                        <li key={idx} className="text-muted-foreground">{instruction}</li>
                                    ))}
                                </ol>
                            </div>

                            <div>
                                <h4 className="text-sm font-medium mb-2">Supported Models:</h4>
                                <div className="flex flex-wrap gap-2">
                                    {provider.models.map(model => (
                                        <Badge key={model} variant="outline" className="text-xs">
                                            {model}
                                        </Badge>
                                    ))}
                                </div>
                            </div>

                            <div>
                                <h4 className="text-sm font-medium mb-2">API Key Format:</h4>
                                <div className="bg-muted p-2 rounded text-sm font-mono">
                                    {provider.apiKeyPlaceholder}
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="h-6 w-6 p-0 ml-2"
                                        onClick={() => navigator.clipboard.writeText(provider.apiKeyPlaceholder)}
                                    >
                                        <Copy className="h-3 w-3" />
                                    </Button>
                                </div>
                            </div>
                        </TabsContent>
                    ))}
                </Tabs>
            </CardContent>
        </Card>
    );
}

# UAUI Protocol - Quick Start Guide

## Get UAUI Running in 10 Minutes

This guide gets you from zero to a working UAUI system in under 10 minutes. Perfect for developers who want to see immediate results.

## Prerequisites (2 minutes)

### What You Need
- **Node.js 16+** installed ([download here](https://nodejs.org/))
- **At least one AI provider API key** (we'll use OpenAI for this guide)
- **Basic terminal/command line knowledge**

### Get Your OpenAI API Key
1. Go to [platform.openai.com](https://platform.openai.com)
2. Sign up or log in
3. Click "API Keys" in the left menu
4. Click "Create new secret key"
5. Copy the key (starts with `sk-`)
6. **Keep this safe** - you'll need it in step 3

## Step 1: Install UAUI (1 minute)

```bash
# Create a new project
mkdir my-uaui-app
cd my-uaui-app

# Initialize npm project
npm init -y

# Install UAUI Protocol
npm install uaui-protocol

# Install additional dependencies for this demo
npm install express dotenv
```

## Step 2: Create Environment File (30 seconds)

Create a `.env` file in your project root:

```bash
# .env
OPENAI_API_KEY=sk-your-actual-openai-key-here
PORT=3000
```

**⚠️ Important**: Replace `sk-your-actual-openai-key-here` with your real OpenAI API key from the prerequisites.

## Step 3: Create Your App (2 minutes)

Create `app.js`:

```javascript
// app.js
require('dotenv').config();
const express = require('express');
const { createUAUIFromExisting } = require('uaui-protocol');

const app = express();
app.use(express.json());

// Configure AI providers
const providers = [
  {
    id: 'openai-1',
    name: 'OpenAI GPT-4',
    type: 'openai',
    apiKey: process.env.OPENAI_API_KEY,
    defaultModel: 'gpt-4',
    maxTokens: 1000,
    temperature: 0.7,
    timeout: 30000
  }
];

// Initialize UAUI
const uaui = createUAUIFromExisting(providers, {
  environment: 'development',
  logLevel: 'info',
  selectionStrategy: 'smart'
});

// Simple web interface
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>UAUI Quick Start</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
            .chat-container { border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .message { margin: 10px 0; padding: 10px; border-radius: 5px; }
            .user { background: #e3f2fd; text-align: right; }
            .ai { background: #f3e5f5; }
            .actions { background: #e8f5e8; font-style: italic; }
            input, button { padding: 10px; margin: 5px; }
            input { width: 300px; }
            button { background: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer; }
            button:hover { background: #1976d2; }
            .status { padding: 10px; margin: 10px 0; border-radius: 4px; }
            .success { background: #d4edda; color: #155724; }
            .error { background: #f8d7da; color: #721c24; }
        </style>
    </head>
    <body>
        <h1>🚀 UAUI Protocol Quick Start</h1>
        <div id="status" class="status">Initializing UAUI...</div>
        
        <div class="chat-container">
            <h3>Try These Commands:</h3>
            <ul>
                <li>"Change the background to blue" - Tests UI actions</li>
                <li>"Show me analytics" - Tests navigation actions</li>
                <li>"What's 2+2?" - Tests simple AI response</li>
                <li>"Analyze this data: sales increased 20%" - Tests complex AI</li>
            </ul>
            
            <div id="messages"></div>
            
            <div>
                <input type="text" id="messageInput" placeholder="Type your message here..." />
                <button onclick="sendMessage()">Send</button>
            </div>
        </div>

        <script>
            let isInitialized = false;

            // Initialize UAUI
            fetch('/api/init', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    isInitialized = data.success;
                    const status = document.getElementById('status');
                    if (data.success) {
                        status.textContent = '✅ UAUI initialized successfully!';
                        status.className = 'status success';
                    } else {
                        status.textContent = '❌ UAUI initialization failed: ' + data.error;
                        status.className = 'status error';
                    }
                })
                .catch(error => {
                    document.getElementById('status').textContent = '❌ Connection error: ' + error.message;
                    document.getElementById('status').className = 'status error';
                });

            function addMessage(content, type) {
                const messages = document.getElementById('messages');
                const message = document.createElement('div');
                message.className = 'message ' + type;
                message.textContent = content;
                messages.appendChild(message);
                messages.scrollTop = messages.scrollHeight;
            }

            function executeActions(actions) {
                if (!actions || actions.length === 0) return;
                
                actions.forEach(action => {
                    let actionText = 'Action executed: ' + action.type;
                    
                    switch(action.type) {
                        case 'widget.appearance.update':
                            if (action.payload.primaryColor) {
                                document.body.style.backgroundColor = action.payload.primaryColor + '20';
                                actionText += ' - Changed background color';
                            }
                            break;
                        case 'cross_app.navigate':
                            actionText += ' - Would navigate to: ' + action.payload.view;
                            break;
                        default:
                            actionText += ' - ' + JSON.stringify(action.payload);
                    }
                    
                    addMessage(actionText, 'actions');
                });
            }

            async function sendMessage() {
                if (!isInitialized) {
                    alert('UAUI is not initialized yet. Please wait.');
                    return;
                }

                const input = document.getElementById('messageInput');
                const message = input.value.trim();
                if (!message) return;

                addMessage('You: ' + message, 'user');
                input.value = '';

                try {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ message })
                    });

                    const data = await response.json();
                    
                    if (data.success) {
                        addMessage('AI: ' + data.response.message, 'ai');
                        if (data.response.actions) {
                            executeActions(data.response.actions);
                        }
                    } else {
                        addMessage('Error: ' + data.error, 'error');
                    }
                } catch (error) {
                    addMessage('Connection error: ' + error.message, 'error');
                }
            }

            // Allow Enter key to send message
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        </script>
    </body>
    </html>
  `);
});

// API endpoint to initialize UAUI
app.post('/api/init', async (req, res) => {
  try {
    await uaui.initialize();
    console.log('✅ UAUI initialized successfully');
    res.json({ success: true });
  } catch (error) {
    console.error('❌ UAUI initialization failed:', error.message);
    res.json({ success: false, error: error.message });
  }
});

// API endpoint for chat
app.post('/api/chat', async (req, res) => {
  try {
    const { message } = req.body;
    
    const request = {
      id: `msg-${Date.now()}`,
      message: message,
      context: {
        app: 'quick-start-demo',
        timestamp: Date.now()
      }
    };

    const response = await uaui.processAIRequest(request);
    
    console.log(`📤 User: ${message}`);
    console.log(`📥 AI: ${response.message}`);
    if (response.actions && response.actions.length > 0) {
      console.log(`🎯 Actions: ${response.actions.length} generated`);
    }

    res.json({ success: true, response });
  } catch (error) {
    console.error('❌ Chat error:', error.message);
    res.json({ success: false, error: error.message });
  }
});

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const providers = await uaui.getAvailableProviders();
    res.json({ 
      status: 'healthy', 
      providers: providers.length,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(503).json({ 
      status: 'unhealthy', 
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Start server
const port = process.env.PORT || 3000;
app.listen(port, () => {
  console.log(`
🚀 UAUI Quick Start Server Running!

📍 Open your browser and go to: http://localhost:${port}

🔧 What's happening:
   • UAUI Protocol is initializing with OpenAI
   • Web interface is ready for testing
   • Try the example commands to see UAUI in action

📊 Health check: http://localhost:${port}/health

🛑 To stop: Press Ctrl+C
  `);
});
```

## Step 4: Run Your App (30 seconds)

```bash
# Start the application
node app.js
```

You should see:
```
🚀 UAUI Quick Start Server Running!

📍 Open your browser and go to: http://localhost:3000
```

## Step 5: Test UAUI (3 minutes)

1. **Open your browser** and go to `http://localhost:3000`
2. **Wait for initialization** (you'll see "✅ UAUI initialized successfully!")
3. **Try these test commands**:

### Basic AI Response
Type: `What's 2+2?`
**Expected**: Simple math answer from AI

### UI Action Test
Type: `Change the background to blue`
**Expected**: AI response + background color changes

### Navigation Action Test
Type: `Show me analytics`
**Expected**: AI response + action message about navigation

### Complex Analysis Test
Type: `Analyze this data: sales increased 20% last month`
**Expected**: Detailed AI analysis

## Step 6: Verify Everything Works (1 minute)

### Check Health Status
Open a new terminal and run:
```bash
curl http://localhost:3000/health
```

You should see:
```json
{
  "status": "healthy",
  "providers": 1,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Check Console Output
In your app terminal, you should see logs like:
```
📤 User: What's 2+2?
📥 AI: 2+2 equals 4.
📤 User: Change the background to blue
📥 AI: I'll change the background color to blue for you.
🎯 Actions: 1 generated
```

## 🎉 Success! What You've Accomplished

In 10 minutes, you've:
- ✅ **Installed UAUI Protocol**
- ✅ **Connected to OpenAI**
- ✅ **Created a working web interface**
- ✅ **Tested AI responses**
- ✅ **Seen automatic UI actions**
- ✅ **Verified system health**

## Next Steps

### Add More AI Providers (5 minutes)
```javascript
// Add to your providers array in app.js
{
  id: 'groq-1',
  name: 'Groq Llama3',
  type: 'groq',
  apiKey: 'your-groq-api-key',
  defaultModel: 'llama3-70b-8192'
}
```

### Enable Smart Provider Selection
With multiple providers, UAUI will automatically:
- Use **Groq** for quick questions (faster)
- Use **OpenAI** for complex analysis (smarter)
- **Fallback** if one provider fails

### Add React Integration
```bash
# Install React dependencies
npm install react react-dom

# Use UAUI React hooks
import { useUAUI } from 'uaui-protocol';
```

## Troubleshooting

### "UAUI initialization failed"
**Problem**: API key issues
**Solution**: 
1. Check your `.env` file has the correct OpenAI API key
2. Verify the key starts with `sk-`
3. Make sure you have credits in your OpenAI account

### "Connection error"
**Problem**: Network or server issues
**Solution**:
1. Check your internet connection
2. Verify OpenAI service status at [status.openai.com](https://status.openai.com)
3. Restart your app with `node app.js`

### "No response from AI"
**Problem**: Request timeout or API limits
**Solution**:
1. Check your OpenAI usage limits
2. Try a simpler message
3. Wait a moment and try again

### Port already in use
**Problem**: Port 3000 is busy
**Solution**:
```bash
# Use a different port
PORT=3001 node app.js
```

## What's Next?

### For Developers
📖 **Read the [Developer Guide](./DEVELOPER-GUIDE.md)** for:
- Advanced API usage
- React integration
- Error handling
- Testing strategies

### For Business Owners
💼 **Read the [Business Guide](./BUSINESS-GUIDE.md)** for:
- ROI calculations
- Cost-benefit analysis
- Implementation planning

### For System Administrators
🔧 **Read the [Admin Guide](./ADMIN-GUIDE.md)** for:
- Production deployment
- Security configuration
- Monitoring setup

### For End Users
👥 **Read the [User Manual](./USER-MANUAL.md)** for:
- Provider configuration
- Using the interface
- Best practices

## Real-World Examples

### E-commerce Customer Support
```javascript
// Customer asks: "Where is my order #12345?"
// UAUI automatically:
// 1. Understands the intent
// 2. Generates helpful response
// 3. Creates action to open order tracking
// 4. Pre-fills order number
```

### Analytics Dashboard
```javascript
// User says: "Show me last month's sales data"
// UAUI automatically:
// 1. Analyzes the request
// 2. Provides data insights
// 3. Navigates to analytics page
// 4. Sets date filter to last month
```

### Content Management
```javascript
// Writer says: "Make this heading blue and bigger"
// UAUI automatically:
// 1. Understands the styling request
// 2. Confirms the change
// 3. Updates CSS properties
// 4. Shows live preview
```

## Support

- **GitHub Issues**: [Report problems](https://github.com/uaui-protocol/uaui/issues)
- **Documentation**: [Complete guides](./CODEBASE-REVIEW-DOCUMENTATION.md)
- **Community**: [Join Discord](https://discord.gg/uaui)

---

**🎯 Goal Achieved**: You now have a working UAUI system that demonstrates intelligent AI provider management and automatic UI actions!

**⏱️ Total Time**: ~10 minutes
**💰 Cost**: Free (using your own API keys)
**🚀 Result**: Production-ready foundation for AI-powered applications

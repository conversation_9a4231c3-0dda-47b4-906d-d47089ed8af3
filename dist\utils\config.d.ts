/**
 * Configuration Utilities for UAUI Protocol
 */
import type { UAUIConfig, AIProvider, UAUIAppConfig } from '../types';
export interface CreateUAUIConfigOptions {
    environment?: 'development' | 'staging' | 'production';
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
    providers?: AIProvider[];
    apps?: Record<string, UAUIAppConfig>;
    selectionStrategy?: 'smart' | 'round_robin' | 'least_latency';
    maxConcurrentRequests?: number;
    defaultTimeout?: number;
}
/**
 * Create a UAUI configuration with defaults
 */
export declare function createUAUIConfig(options?: CreateUAUIConfigOptions): UAUIConfig;
/**
 * Validate UAUI configuration
 */
export declare function validateConfig(config: UAUIConfig): {
    valid: boolean;
    errors: string[];
};
/**
 * Merge configurations with deep merge
 */
export declare function mergeConfigs(base: UAUIConfig, override: Partial<UAUIConfig>): UAUIConfig;
/**
 * Get default configuration
 */
export declare function getDefaultConfig(): UAUIConfig;
//# sourceMappingURL=config.d.ts.map
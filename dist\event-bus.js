"use strict";
/**
 * UAUI Event Bus
 * High-performance event system for cross-app communication
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.metricsMiddleware = exports.validationMiddleware = exports.rateLimitMiddleware = exports.loggingMiddleware = exports.EventBus = void 0;
const events_1 = require("events");
class EventBus {
    constructor(logger) {
        this.eventListeners = new Map();
        this.middleware = [];
        this.eventHistory = [];
        this.maxHistorySize = 1000;
        this.isInitialized = false;
        this.nodeEmitter = new events_1.EventEmitter();
        this.logger = logger;
        this.nodeEmitter.setMaxListeners(100); // Increase default limit
    }
    async initialize() {
        if (this.isInitialized)
            return;
        this.logger.info('Initializing Event Bus...');
        this.setupErrorHandling();
        this.isInitialized = true;
        this.logger.info('Event Bus initialized');
    }
    /**
     * Emit an event through the bus
     */
    emit(event) {
        try {
            // Validate event
            this.validateEvent(event);
            // Add to history
            this.addToHistory(event);
            // Apply middleware
            this.applyMiddleware(event, () => {
                // Route to listeners
                this.routeToListeners(event);
                // Handle cross-app routing
                if (event.metadata?.crossApp) {
                    this.routeCrossApp(event);
                }
                // Emit on EventEmitter for compatibility
                this.nodeEmitter.emit(event.type, event);
                this.nodeEmitter.emit('*', event); // Wildcard listener
            });
            this.logger.debug(`Event emitted: ${event.type}`, { id: event.id });
            return true;
        }
        catch (error) {
            this.logger.error('Failed to emit event:', error);
            this.emitError(event, error);
            return false;
        }
    }
    /**
     * Subscribe to events
     */
    on(eventType, listener) {
        if (eventType === '*') {
            // Wildcard listener
            this.nodeEmitter.on('*', listener);
            return () => this.nodeEmitter.off('*', listener);
        }
        if (!this.eventListeners.has(eventType)) {
            this.eventListeners.set(eventType, new Set());
        }
        this.eventListeners.get(eventType).add(listener);
        this.logger.debug(`Listener added for event: ${eventType}`);
        // Return unsubscribe function
        return () => {
            this.eventListeners.get(eventType)?.delete(listener);
            if (this.eventListeners.get(eventType)?.size === 0) {
                this.eventListeners.delete(eventType);
            }
        };
    }
    /**
     * Subscribe to events once
     */
    once(eventType, listener) {
        const onceListener = (event) => {
            listener(event);
            unsubscribe();
        };
        const unsubscribe = this.on(eventType, onceListener);
        return unsubscribe;
    }
    /**
     * Remove all listeners for an event type
     */
    off(eventType) {
        this.eventListeners.delete(eventType);
        this.nodeEmitter.removeAllListeners(eventType);
        this.logger.debug(`All listeners removed for event: ${eventType}`);
    }
    /**
     * Add middleware to the event pipeline
     */
    use(middleware) {
        this.middleware.push(middleware);
        this.logger.debug('Middleware added to event bus');
    }
    /**
     * Get event history
     */
    getHistory(filter) {
        let filtered = [...this.eventHistory];
        if (filter) {
            if (filter.type) {
                filtered = filtered.filter(e => e.type === filter.type);
            }
            if (filter.source) {
                filtered = filtered.filter(e => e.source.app === filter.source);
            }
            if (filter.target) {
                filtered = filtered.filter(e => e.target?.app === filter.target);
            }
            if (filter.since) {
                filtered = filtered.filter(e => e.timestamp >= filter.since);
            }
            if (filter.limit) {
                filtered = filtered.slice(-filter.limit);
            }
        }
        return filtered;
    }
    /**
     * Clear event history
     */
    clearHistory() {
        this.eventHistory = [];
        this.logger.debug('Event history cleared');
    }
    /**
     * Get listener count for an event type
     */
    listenerCount(eventType) {
        return this.eventListeners.get(eventType)?.size || 0;
    }
    /**
     * Get all event types with listeners
     */
    getEventTypes() {
        return Array.from(this.eventListeners.keys());
    }
    /**
     * Shutdown the event bus
     */
    async shutdown() {
        this.logger.info('Shutting down Event Bus...');
        this.eventListeners.clear();
        this.middleware = [];
        this.eventHistory = [];
        this.nodeEmitter.removeAllListeners();
        this.isInitialized = false;
        this.logger.info('Event Bus shut down');
    }
    // Private methods
    validateEvent(event) {
        if (!event.id || !event.type || !event.timestamp || !event.source) {
            throw new Error('Invalid event: missing required fields');
        }
        if (typeof event.timestamp !== 'number' || event.timestamp <= 0) {
            throw new Error('Invalid event: invalid timestamp');
        }
    }
    addToHistory(event) {
        this.eventHistory.push(event);
        // Trim history if it exceeds max size
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
        }
    }
    applyMiddleware(event, next) {
        let index = 0;
        const runMiddleware = () => {
            if (index >= this.middleware.length) {
                next();
                return;
            }
            const middleware = this.middleware[index++];
            try {
                middleware(event, runMiddleware);
            }
            catch (error) {
                this.logger.error('Middleware error:', error);
                this.emitError(event, error);
            }
        };
        runMiddleware();
    }
    routeToListeners(event) {
        const listeners = this.eventListeners.get(event.type);
        if (!listeners || listeners.size === 0) {
            return;
        }
        for (const listener of listeners) {
            try {
                const result = listener(event);
                // Handle async listeners
                if (result instanceof Promise) {
                    result.catch(error => {
                        this.logger.error('Async listener error:', error);
                        this.emitError(event, error);
                    });
                }
            }
            catch (error) {
                this.logger.error('Listener error:', error);
                this.emitError(event, error);
            }
        }
    }
    routeCrossApp(event) {
        if (!event.target?.app) {
            this.logger.warn('Cross-app event without target app:', event.id);
            return;
        }
        // Emit cross-app routing event
        this.nodeEmitter.emit('cross_app_route', {
            ...event,
            type: 'cross_app.route',
            id: `route-${event.id}`,
            timestamp: Date.now()
        });
    }
    setupErrorHandling() {
        this.on('error', (event) => {
            this.logger.error('Event bus error:', event.data);
        });
        // Handle uncaught errors
        process.on('uncaughtException', (error) => {
            this.logger.error('Uncaught exception in event bus:', error);
        });
        process.on('unhandledRejection', (reason) => {
            this.logger.error('Unhandled rejection in event bus:', reason);
        });
    }
    emitError(originalEvent, error) {
        const errorEvent = {
            id: `error-${Date.now()}`,
            type: 'system.error',
            timestamp: Date.now(),
            source: { app: 'uaui-event-bus' },
            data: {
                originalEvent,
                error: {
                    message: error.message,
                    stack: error.stack,
                    timestamp: Date.now()
                }
            },
            metadata: {
                priority: 'high'
            }
        };
        // Emit error event (but don't recurse if this fails)
        try {
            this.nodeEmitter.emit('error', errorEvent);
        }
        catch (emitError) {
            this.logger.error('Failed to emit error event:', emitError);
        }
    }
}
exports.EventBus = EventBus;
// ============================================================================
// BUILT-IN MIDDLEWARE
// ============================================================================
/**
 * Logging middleware
 */
const loggingMiddleware = (logger) => {
    return (event, next) => {
        logger.debug(`Event: ${event.type}`, {
            id: event.id,
            source: event.source.app,
            target: event.target?.app
        });
        next();
    };
};
exports.loggingMiddleware = loggingMiddleware;
/**
 * Rate limiting middleware
 */
const rateLimitMiddleware = (maxEvents = 100, windowMs = 60000) => {
    const eventCounts = new Map();
    return (event, next) => {
        const key = `${event.source.app}:${event.type}`;
        const now = Date.now();
        let bucket = eventCounts.get(key);
        if (!bucket || now > bucket.resetTime) {
            bucket = { count: 0, resetTime: now + windowMs };
            eventCounts.set(key, bucket);
        }
        if (bucket.count >= maxEvents) {
            throw new Error(`Rate limit exceeded for ${key}`);
        }
        bucket.count++;
        next();
    };
};
exports.rateLimitMiddleware = rateLimitMiddleware;
/**
 * Validation middleware
 */
const validationMiddleware = () => {
    return (event, next) => {
        // Additional validation beyond basic checks
        if (event.metadata?.priority && !['low', 'normal', 'high', 'critical'].includes(event.metadata.priority)) {
            throw new Error('Invalid priority level');
        }
        if (event.metadata?.ttl && event.metadata.ttl <= 0) {
            throw new Error('Invalid TTL value');
        }
        // Validate event structure
        if (!event.id || typeof event.id !== 'string') {
            throw new Error('Event must have a valid string ID');
        }
        if (!event.type || typeof event.type !== 'string') {
            throw new Error('Event must have a valid string type');
        }
        if (!event.source || !event.source.app) {
            throw new Error('Event must have a valid source app');
        }
        next();
    };
};
exports.validationMiddleware = validationMiddleware;
/**
 * Metrics middleware
 */
const metricsMiddleware = () => {
    const metrics = {
        totalEvents: 0,
        eventsByType: new Map(),
        eventsByApp: new Map()
    };
    return (event, next) => {
        metrics.totalEvents++;
        const typeCount = metrics.eventsByType.get(event.type) || 0;
        metrics.eventsByType.set(event.type, typeCount + 1);
        const appCount = metrics.eventsByApp.get(event.source.app) || 0;
        metrics.eventsByApp.set(event.source.app, appCount + 1);
        // Attach metrics to global object for monitoring
        global.uauiMetrics = metrics;
        next();
    };
};
exports.metricsMiddleware = metricsMiddleware;
//# sourceMappingURL=event-bus.js.map
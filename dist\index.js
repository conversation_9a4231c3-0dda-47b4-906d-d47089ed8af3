"use strict";
/**
 * UAUI System - Main Export File
 * Exports all necessary components and types for integration
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_CONFIG = exports.VERSION = exports.getDefaultConfig = exports.mergeConfigs = exports.validateConfig = exports.createUAUIConfig = exports.Logger = exports.GroqAdapter = exports.MistralAdapter = exports.GeminiAdapter = exports.ClaudeAdapter = exports.OpenAIAdapter = exports.AIProviderAdapter = exports.UniversalAIService = exports.AppRouter = exports.StateManager = exports.EventBus = exports.UAUIFactory = exports.UAUICore = exports.createUAUIFromExisting = exports.UAUI = void 0;
// Core UAUI exports
var uaui_1 = require("./uaui");
Object.defineProperty(exports, "UAUI", { enumerable: true, get: function () { return uaui_1.UAUI; } });
Object.defineProperty(exports, "createUAUIFromExisting", { enumerable: true, get: function () { return uaui_1.createUAUIFromExisting; } });
var core_1 = require("./core");
Object.defineProperty(exports, "UAUICore", { enumerable: true, get: function () { return core_1.UAUICore; } });
var factory_1 = require("./factory");
Object.defineProperty(exports, "UAUIFactory", { enumerable: true, get: function () { return factory_1.UAUIFactory; } });
var event_bus_1 = require("./event-bus");
Object.defineProperty(exports, "EventBus", { enumerable: true, get: function () { return event_bus_1.EventBus; } });
var state_manager_1 = require("./state-manager");
Object.defineProperty(exports, "StateManager", { enumerable: true, get: function () { return state_manager_1.StateManager; } });
var app_router_1 = require("./app-router");
Object.defineProperty(exports, "AppRouter", { enumerable: true, get: function () { return app_router_1.AppRouter; } });
// AI Service exports
var ai_service_1 = require("./ai-service");
Object.defineProperty(exports, "UniversalAIService", { enumerable: true, get: function () { return ai_service_1.UniversalAIService; } });
Object.defineProperty(exports, "AIProviderAdapter", { enumerable: true, get: function () { return ai_service_1.AIProviderAdapter; } });
Object.defineProperty(exports, "OpenAIAdapter", { enumerable: true, get: function () { return ai_service_1.OpenAIAdapter; } });
Object.defineProperty(exports, "ClaudeAdapter", { enumerable: true, get: function () { return ai_service_1.ClaudeAdapter; } });
Object.defineProperty(exports, "GeminiAdapter", { enumerable: true, get: function () { return ai_service_1.GeminiAdapter; } });
Object.defineProperty(exports, "MistralAdapter", { enumerable: true, get: function () { return ai_service_1.MistralAdapter; } });
Object.defineProperty(exports, "GroqAdapter", { enumerable: true, get: function () { return ai_service_1.GroqAdapter; } });
// Utility exports
var logger_1 = require("./utils/logger");
Object.defineProperty(exports, "Logger", { enumerable: true, get: function () { return logger_1.Logger; } });
var config_1 = require("./utils/config");
Object.defineProperty(exports, "createUAUIConfig", { enumerable: true, get: function () { return config_1.createUAUIConfig; } });
Object.defineProperty(exports, "validateConfig", { enumerable: true, get: function () { return config_1.validateConfig; } });
Object.defineProperty(exports, "mergeConfigs", { enumerable: true, get: function () { return config_1.mergeConfigs; } });
Object.defineProperty(exports, "getDefaultConfig", { enumerable: true, get: function () { return config_1.getDefaultConfig; } });
// Version
exports.VERSION = '1.0.0';
// Default configuration
exports.DEFAULT_CONFIG = {
    core: {
        version: exports.VERSION,
        environment: 'development',
        logLevel: 'info',
        maxConcurrentRequests: 10,
        defaultTimeout: 30000
    },
    ai: {
        providers: [],
        selectionStrategy: 'smart',
        fallbackEnabled: true,
        caching: {
            enabled: true,
            ttl: 300000, // 5 minutes
            maxSize: 100
        }
    },
    apps: {}
};
//# sourceMappingURL=index.js.map
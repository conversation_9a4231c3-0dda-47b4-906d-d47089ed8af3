"use strict";
/**
 * UAUI Integration Service for Tempo Widget
 * Bridges Tempo Widget's AI providers with the UAUI system
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.UAUIIntegrationService = void 0;
const __1 = require("../..");
const ai_provider_store_1 = require("@/stores/ai-provider-store");
/**
 * UAUI Integration Service
 * Manages UAUI lifecycle and integration with Tempo Widget system
 */
class UAUIIntegrationService {
    constructor(config = {
        environment: 'development',
        logLevel: 'info',
        selectionStrategy: 'smart'
    }) {
        this.uaui = null;
        this.isInitialized = false;
        this.config = config;
    }
    /**
     * Initialize UAUI with current AI providers from Tempo Widget
     */
    async initialize() {
        try {
            // Get AI providers from Tempo Widget store
            const { providers } = ai_provider_store_1.useAIProviderStore.getState();
            // Filter active and configured providers
            const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];
            if (activeProviders.length === 0) {
                console.warn('No active AI providers found for UAUI initialization');
                return false;
            }
            // Convert Tempo providers to UAUI format
            const uauiProviders = activeProviders.map(provider => ({
                id: provider.id,
                name: provider.name,
                type: provider.type,
                apiKey: provider.apiKey,
                models: provider.models || [],
                defaultModel: provider.models?.[0] || 'default',
                maxTokens: 4000,
                temperature: 0.7,
                timeout: 30000
            }));
            console.log(`🔧 Converting ${uauiProviders.length} Tempo providers to UAUI format`);
            // Create UAUI instance
            this.uaui = (0, __1.createUAUIFromExisting)(uauiProviders, {
                environment: this.config.environment || 'development',
                logLevel: this.config.logLevel || 'info',
                selectionStrategy: this.config.selectionStrategy || 'smart'
            });
            // Initialize UAUI
            await this.uaui.initialize();
            this.isInitialized = true;
            console.log('✅ UAUI Integration Service initialized successfully');
            return true;
        }
        catch (error) {
            console.error('❌ UAUI Integration Service initialization failed:', error);
            this.isInitialized = false;
            return false;
        }
    }
    /**
     * Reinitialize UAUI with updated providers
     */
    async reinitialize() {
        console.log('🔄 Reinitializing UAUI Integration Service...');
        if (this.uaui) {
            await this.shutdown();
        }
        return await this.initialize();
    }
    /**
     * Shutdown UAUI
     */
    async shutdown() {
        if (this.uaui) {
            await this.uaui.shutdown();
            this.uaui = null;
        }
        this.isInitialized = false;
        console.log('🛑 UAUI Integration Service shutdown complete');
    }
    /**
     * Send message to UAUI
     */
    async sendMessage(message) {
        if (!this.isInitialized || !this.uaui) {
            throw new Error('UAUI Integration Service is not initialized');
        }
        try {
            const response = await this.uaui.processAIRequest({
                id: message.id,
                message: message.message,
                context: message.context || {},
                metadata: {
                    source: 'tempo-widget',
                    timestamp: Date.now(),
                    ...message.metadata
                }
            });
            return response;
        }
        catch (error) {
            console.error('❌ UAUI message processing failed:', error);
            throw error;
        }
    }
    /**
     * Get available providers
     */
    getAvailableProviders() {
        if (!this.uaui)
            return [];
        try {
            return this.uaui.getAvailableProviders();
        }
        catch (error) {
            console.error('❌ Failed to get available providers:', error);
            return [];
        }
    }
    /**
     * Check if UAUI is ready
     */
    isReady() {
        return this.isInitialized && this.uaui !== null;
    }
    /**
     * Get UAUI status
     */
    getStatus() {
        return {
            initialized: this.isInitialized,
            hasInstance: this.uaui !== null,
            providers: this.getAvailableProviders(),
            config: this.config
        };
    }
    /**
     * Update configuration
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }
}
exports.UAUIIntegrationService = UAUIIntegrationService;
// Create singleton instance
const uauiIntegrationService = new UAUIIntegrationService();
exports.default = uauiIntegrationService;
//# sourceMappingURL=uaui-integration-service.js.map
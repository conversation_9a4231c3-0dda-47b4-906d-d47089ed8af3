{"version": 3, "file": "page.js", "sourceRoot": "", "sources": ["../../src/uaui-system/page.tsx"], "names": [], "mappings": ";AAAA;;;GAGG;AAEH,YAAY,CAAC;AALb;;;GAGG;;;;;AA4BH,iCA0RC;AAlTD,kDAA0B;AAC1B,+CAAgF;AAChF,mDAAgD;AAChD,iDAA8C;AAC9C,iDAAgE;AAChE,+CAAgF;AAChF,+CAYsB;AACtB,4FAA6F;AAC7F,kEAAgE;AAChE,qDAA6B;AAC7B,kCAA2E;AAE3E,SAAwB,cAAc;IACpC,MAAM,EACJ,aAAa,EACb,SAAS,EACT,KAAK,EACL,kBAAkB,EAClB,UAAU,EACV,YAAY,EACb,GAAG,IAAA,cAAO,GAAE,CAAC;IAEd,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,sCAAkB,GAAE,CAAC;IAC3C,MAAM,eAAe,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAE5G,MAAM,cAAc,GAAG,KAAK,IAAI,EAAE;QAChC,0BAA0B;QAC1B,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;IACxC,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CACpB,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,CACtC;MAAA,CAAC,cAAI,CAAC,IAAI,CAAC,6BAA6B,CACtC;QAAA,CAAC,eAAM,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CACzC;UAAA,CAAC,qBAAM,CAAC,SAAS,CAAC,SAAS,EAC3B;;QACF,EAAE,eAAM,CACV;MAAA,EAAE,cAAI,CACN;MAAA,CAAC,eAAM,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,cAAc,CAAC,CAChD;QAAA,CAAC,uBAAQ,CAAC,SAAS,CAAC,cAAc,EAClC;;MACF,EAAE,eAAM,CACR;MAAA,CAAC,eAAM,CAAC,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,CACjD;QAAA,CAAC,uBAAQ,CAAC,SAAS,CAAC,cAAc,EAClC;;MACF,EAAE,eAAM,CACV;IAAA,EAAE,GAAG,CAAC,CACP,CAAC;IAEF,OAAO,CACL,CAAC,qDAA2B,CAC1B,SAAS,CAAC,eAAe,CACzB,eAAe,CAAC,wCAAwC,CACxD,aAAa,CAAC,CAAC,aAAa,CAAC,CAE7B;MAAA,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CACxB;QAAA,CAAC,4BAA4B,CAC7B;QAAA,CAAC,GAAG,CAAC,SAAS,CAAC,uCAAuC,CACpD;UAAA,CAAC,WAAI,CACH;YAAA,CAAC,iBAAU,CAAC,SAAS,CAAC,2DAA2D,CAC/E;cAAA,CAAC,gBAAS,CAAC,SAAS,CAAC,qBAAqB,CAAC,aAAa,EAAE,gBAAS,CACnE;cAAA,CAAC,oBAAK,CAAC,SAAS,CAAC,+BAA+B,EAClD;YAAA,EAAE,iBAAU,CACZ;YAAA,CAAC,kBAAW,CACV;cAAA,CAAC,GAAG,CAAC,SAAS,CAAC,oBAAoB,CACjC;gBAAA,CAAC,aAAa,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CACxC;cAAA,EAAE,GAAG,CACL;cAAA,CAAC,GAAG,CAAC,SAAS,CAAC,8BAA8B,CAC3C;gBAAA,CAAC,aAAa,CAAC,CAAC,CAAC,CACf,CAAC,0BAAW,CAAC,SAAS,CAAC,wBAAwB,EAAG,CACnD,CAAC,CAAC,CAAC,CACF,CAAC,0BAAW,CAAC,SAAS,CAAC,yBAAyB,EAAG,CACpD,CACD;gBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAC1C;kBAAA,CAAC,aAAa,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,2BAA2B,CAClE;gBAAA,EAAE,CAAC,CACL;cAAA,EAAE,GAAG,CACP;YAAA,EAAE,kBAAW,CACf;UAAA,EAAE,WAAI,CAEN;;UAAA,CAAC,WAAI,CACH;YAAA,CAAC,iBAAU,CAAC,SAAS,CAAC,2DAA2D,CAC/E;cAAA,CAAC,gBAAS,CAAC,SAAS,CAAC,qBAAqB,CAAC,YAAY,EAAE,gBAAS,CAClE;cAAA,CAAC,kBAAG,CAAC,SAAS,CAAC,+BAA+B,EAChD;YAAA,EAAE,iBAAU,CACZ;YAAA,CAAC,kBAAW,CACV;cAAA,CAAC,GAAG,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,kBAAkB,CAAC,MAAM,CAAC,EAAE,GAAG,CACpE;cAAA,CAAC,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAC1C;gBAAA,CAAC,eAAe,CAAC,MAAM,CAAE,aAAY,CAAC,kBAAkB,CAAC,MAAM,CAAE;cACnE,EAAE,CAAC,CACL;YAAA,EAAE,kBAAW,CACf;UAAA,EAAE,WAAI,CAEN;;UAAA,CAAC,WAAI,CACH;YAAA,CAAC,iBAAU,CAAC,SAAS,CAAC,2DAA2D,CAC/E;cAAA,CAAC,gBAAS,CAAC,SAAS,CAAC,qBAAqB,CAAC,WAAW,EAAE,gBAAS,CACjE;cAAA,CAAC,wBAAS,CAAC,SAAS,CAAC,+BAA+B,EACtD;YAAA,EAAE,iBAAU,CACZ;YAAA,CAAC,kBAAW,CACV;cAAA,CAAC,GAAG,CAAC,SAAS,CAAC,oBAAoB,CAAC,KAAK,EAAE,GAAG,CAC9C;cAAA,CAAC,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAC1C;;cACF,EAAE,CAAC,CACL;YAAA,EAAE,kBAAW,CACf;UAAA,EAAE,WAAI,CACR;QAAA,EAAE,GAAG,CAEL;;QAAA,CAAC,iBAAiB,CAClB;QAAA,CAAC,KAAK,IAAI,CACR,CAAC,aAAK,CAAC,OAAO,CAAC,aAAa,CAC1B;YAAA,CAAC,0BAAW,CAAC,SAAS,CAAC,SAAS,EAChC;YAAA,CAAC,wBAAgB,CACf;cAAA,CAAC,KAAK,CACR;YAAA,EAAE,wBAAgB,CACpB;UAAA,EAAE,aAAK,CAAC,CACT,CAED;;QAAA,CAAC,0BAA0B,CAC3B;QAAA,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,CAC/B,CAAC,aAAK,CACJ;YAAA,CAAC,mBAAI,CAAC,SAAS,CAAC,SAAS,EACzB;YAAA,CAAC,wBAAgB,CACf;;YACF,EAAE,wBAAgB,CACpB;UAAA,EAAE,aAAK,CAAC,CACT,CAED;;QAAA,CAAC,uBAAuB,CACxB;QAAA,CAAC,WAAI,CAAC,YAAY,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,CACjD;UAAA,CAAC,eAAQ,CACP;YAAA,CAAC,kBAAW,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,kBAAW,CACnD;YAAA,CAAC,kBAAW,CAAC,KAAK,CAAC,WAAW,CAAC,YAAY,EAAE,kBAAW,CACxD;YAAA,CAAC,kBAAW,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,kBAAW,CACrD;YAAA,CAAC,kBAAW,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,EAAE,kBAAW,CACrD;UAAA,EAAE,eAAQ,CAEV;;UAAA,CAAC,kBAAW,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,CACjD;YAAA,CAAC,GAAG,CAAC,SAAS,CAAC,uCAAuC,CACpD;cAAA,CAAC,mBAAmB,CACpB;cAAA,CAAC,WAAI,CACH;gBAAA,CAAC,iBAAU,CACT;kBAAA,CAAC,gBAAS,CAAC,SAAS,CAAC,yBAAyB,CAC5C;oBAAA,CAAC,uBAAQ,CAAC,SAAS,CAAC,yBAAyB,EAC7C;;kBACF,EAAE,gBAAS,CACb;gBAAA,EAAE,iBAAU,CACZ;gBAAA,CAAC,kBAAW,CAAC,SAAS,CAAC,WAAW,CAChC;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,CACtC;oBAAA,CAAC,0BAAW,CAAC,SAAS,CAAC,wBAAwB,EAC/C;oBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,2BAA2B,EAAE,IAAI,CAC7D;kBAAA,EAAE,GAAG,CACL;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,CACtC;oBAAA,CAAC,0BAAW,CAAC,SAAS,CAAC,wBAAwB,EAC/C;oBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,+BAA+B,EAAE,IAAI,CACjE;kBAAA,EAAE,GAAG,CACL;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,CACtC;oBAAA,CAAC,0BAAW,CAAC,SAAS,CAAC,wBAAwB,EAC/C;oBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,wBAAwB,EAAE,IAAI,CAC1D;kBAAA,EAAE,GAAG,CACL;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,CACtC;oBAAA,CAAC,0BAAW,CAAC,SAAS,CAAC,wBAAwB,EAC/C;oBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,wBAAwB,EAAE,IAAI,CAC1D;kBAAA,EAAE,GAAG,CACL;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,CACtC;oBAAA,CAAC,0BAAW,CAAC,SAAS,CAAC,wBAAwB,EAC/C;oBAAA,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,wBAAwB,EAAE,IAAI,CAC1D;kBAAA,EAAE,GAAG,CACP;gBAAA,EAAE,kBAAW,CACf;cAAA,EAAE,WAAI,CAEN;;cAAA,CAAC,mBAAmB,CACpB;cAAA,CAAC,WAAI,CACH;gBAAA,CAAC,iBAAU,CACT;kBAAA,CAAC,gBAAS,CAAC,SAAS,CAAC,yBAAyB,CAC5C;oBAAA,CAAC,uBAAQ,CAAC,SAAS,CAAC,uBAAuB,EAC3C;;kBACF,EAAE,gBAAS,CACb;gBAAA,EAAE,iBAAU,CACZ;gBAAA,CAAC,kBAAW,CAAC,SAAS,CAAC,WAAW,CAChC;kBAAA,CAAC,eAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC,OAAO,CAAC,SAAS,CACxD;oBAAA,CAAC,mBAAI,CAAC,SAAS,CAAC,cAAc,EAC9B;;kBACF,EAAE,eAAM,CACR;kBAAA,CAAC,eAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC,OAAO,CAAC,SAAS,CACxD;oBAAA,CAAC,wBAAS,CAAC,SAAS,CAAC,cAAc,EACnC;;kBACF,EAAE,eAAM,CACR;kBAAA,CAAC,eAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC,OAAO,CAAC,SAAS,CACxD;oBAAA,CAAC,uBAAQ,CAAC,SAAS,CAAC,cAAc,EAClC;;kBACF,EAAE,eAAM,CACR;kBAAA,CAAC,eAAM,CAAC,SAAS,CAAC,sBAAsB,CAAC,OAAO,CAAC,SAAS,CACxD;oBAAA,CAAC,uBAAQ,CAAC,SAAS,CAAC,cAAc,EAClC;;kBACF,EAAE,eAAM,CACV;gBAAA,EAAE,kBAAW,CACf;cAAA,EAAE,WAAI,CACR;YAAA,EAAE,GAAG,CACP;UAAA,EAAE,kBAAW,CAEb;;UAAA,CAAC,kBAAW,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,WAAW,CAClD;YAAA,CAAC,WAAI,CACH;cAAA,CAAC,iBAAU,CACT;gBAAA,CAAC,gBAAS,CAAC,mBAAmB,EAAE,gBAAS,CACzC;gBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAC1C;;gBACF,EAAE,CAAC,CACL;cAAA,EAAE,iBAAU,CACZ;cAAA,CAAC,kBAAW,CACV;gBAAA,CAAC,0BAAmB,CAClB,QAAQ,CAAC,aAAa,CACtB,cAAc,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE;YACzB,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;QACzC,CAAC,CAAC,CACF,gBAAgB,CAAC,CAAC,CAAC,MAAM,EAAE,EAAE;YAC3B,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC,CAAC;QAC1C,CAAC,CAAC,EAEN;cAAA,EAAE,kBAAW,CACf;YAAA,EAAE,WAAI,CACR;UAAA,EAAE,kBAAW,CAEb;;UAAA,CAAC,kBAAW,CAAC,KAAK,CAAC,WAAW,CAAC,SAAS,CAAC,WAAW,CAClD;YAAA,CAAC,yBAAkB,CAAC,AAAD,EAEnB;;YAAA,CAAC,GAAG,CAAC,SAAS,CAAC,mFAAmF,CAChG;cAAA,CAAC,GAAG,CACF;gBAAA,CAAC,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,kCAAkC,EAAE,EAAE,CAClE;gBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAC1C;;gBACF,EAAE,CAAC,CACL;cAAA,EAAE,GAAG,CACL;cAAA,CAAC,cAAI,CAAC,IAAI,CAAC,yBAAyB,CAClC;gBAAA,CAAC,eAAM,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CACzC;kBAAA,CAAC,uBAAQ,CAAC,SAAS,CAAC,SAAS,EAC7B;;gBACF,EAAE,eAAM,CACV;cAAA,EAAE,cAAI,CACR;YAAA,EAAE,GAAG,CAEL;;YAAA,CAAC,WAAI,CACH;cAAA,CAAC,iBAAU,CACT;gBAAA,CAAC,gBAAS,CAAC,sBAAsB,EAAE,gBAAS,CAC5C;gBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAC1C;;gBACF,EAAE,CAAC,CACL;cAAA,EAAE,iBAAU,CACZ;cAAA,CAAC,kBAAW,CACV;gBAAA,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAC/B,CAAC,GAAG,CAAC,SAAS,CAAC,uCAAuC,CACpD;oBAAA,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,CAC3C,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,yDAAyD,CAClF;wBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,CACtC;0BAAA,CAAC,kBAAG,CAAC,SAAS,CAAC,wBAAwB,EACvC;0BAAA,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,EAAE,IAAI,CAChD;wBAAA,EAAE,GAAG,CACL;wBAAA,CAAC,aAAK,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,EAAE,aAAK,CAC1C;sBAAA,EAAE,GAAG,CAAC,CACP,CAAC,CACJ;kBAAA,EAAE,GAAG,CAAC,CACP,CAAC,CAAC,CAAC,CACF,CAAC,GAAG,CAAC,SAAS,CAAC,wCAAwC,CACrD;;kBACF,EAAE,GAAG,CAAC,CACP,CACH;cAAA,EAAE,kBAAW,CACf;YAAA,EAAE,WAAI,CACR;UAAA,EAAE,kBAAW,CAEb;;UAAA,CAAC,kBAAW,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW,CACjD;YAAA,CAAC,WAAI,CACH;cAAA,CAAC,iBAAU,CACT;gBAAA,CAAC,gBAAS,CAAC,kBAAkB,EAAE,gBAAS,CACxC;gBAAA,CAAC,CAAC,CAAC,SAAS,CAAC,+BAA+B,CAC1C;;gBACF,EAAE,CAAC,CACL;cAAA,EAAE,iBAAU,CACZ;cAAA,CAAC,kBAAW,CACV;gBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,CACxB;kBAAA,CAAC,GAAG,CAAC,SAAS,CAAC,+BAA+B,CAC5C;;kBACF,EAAE,GAAG,CACL;kBAAA,CAAC,eAAM,CAAC,OAAO,CAAC,SAAS,CACvB;oBAAA,CAAC,uBAAQ,CAAC,SAAS,CAAC,cAAc,EAClC;;kBACF,EAAE,eAAM,CACV;gBAAA,EAAE,GAAG,CACP;cAAA,EAAE,kBAAW,CACf;YAAA,EAAE,WAAI,CACR;UAAA,EAAE,kBAAW,CACf;QAAA,EAAE,WAAI,CACR;MAAA,EAAE,GAAG,CACP;IAAA,EAAE,qDAA2B,CAAC,CAC/B,CAAC;AACJ,CAAC"}
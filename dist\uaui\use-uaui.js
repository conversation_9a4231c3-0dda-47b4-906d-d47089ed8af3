"use strict";
/**
 * React Hook for UAUI Integration
 * Provides easy access to UAUI functionality in React components
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useUAUI = useUAUI;
exports.useUAUIChat = useUAUIChat;
const react_1 = require("react");
const ai_provider_store_1 = require("@/stores/ai-provider-store");
const uaui_integration_service_1 = __importDefault(require("./uaui-integration-service"));
function useUAUI(options = {}) {
    const { autoInitialize = true, environment = 'development', logLevel = 'info' } = options;
    // State
    const [isInitialized, setIsInitialized] = (0, react_1.useState)(false);
    const [isLoading, setIsLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    const [availableProviders, setAvailableProviders] = (0, react_1.useState)([]);
    // Refs
    const initializationAttempted = (0, react_1.useRef)(false);
    // AI Provider store
    const { providers, fetchProviders } = (0, ai_provider_store_1.useAIProviderStore)();
    /**
     * Initialize UAUI
     */
    const initialize = (0, react_1.useCallback)(async () => {
        if (isLoading)
            return false;
        setIsLoading(true);
        setError(null);
        try {
            // Ensure we have latest providers
            await fetchProviders();
            // Initialize UAUI service
            const success = await uaui_integration_service_1.default.initialize();
            if (success) {
                setIsInitialized(true);
                // Get available providers
                const providers = await uaui_integration_service_1.default.getAvailableProviders();
                setAvailableProviders(providers);
                console.log('✅ UAUI hook initialized successfully');
            }
            else {
                setError('Failed to initialize UAUI - no active AI providers found');
            }
            return success;
        }
        catch (err) {
            const errorMessage = err.message || 'Failed to initialize UAUI';
            setError(errorMessage);
            console.error('❌ UAUI initialization failed:', err);
            return false;
        }
        finally {
            setIsLoading(false);
        }
    }, [fetchProviders, isLoading]);
    /**
     * Process chat message with UAUI
     */
    const processChatMessage = (0, react_1.useCallback)(async (message) => {
        if (!isInitialized) {
            setError('UAUI not initialized');
            return null;
        }
        try {
            setError(null);
            const response = await uaui_integration_service_1.default.processChatMessage(message);
            console.log('✅ UAUI processed message:', {
                provider: response.metadata.provider,
                responseTime: response.metadata.responseTime,
                actions: response.actions?.length || 0
            });
            return response;
        }
        catch (err) {
            const errorMessage = err.message || 'Failed to process message';
            setError(errorMessage);
            console.error('❌ UAUI message processing failed:', err);
            return null;
        }
    }, [isInitialized]);
    /**
     * Reinitialize UAUI (useful when providers change)
     */
    const reinitialize = (0, react_1.useCallback)(async () => {
        setIsInitialized(false);
        setAvailableProviders([]);
        return await initialize();
    }, [initialize]);
    /**
     * Shutdown UAUI
     */
    const shutdown = (0, react_1.useCallback)(async () => {
        try {
            await uaui_integration_service_1.default.shutdown();
            setIsInitialized(false);
            setAvailableProviders([]);
            setError(null);
            console.log('✅ UAUI shut down successfully');
        }
        catch (err) {
            console.error('❌ UAUI shutdown failed:', err);
        }
    }, []);
    /**
     * Clear error state
     */
    const clearError = (0, react_1.useCallback)(() => {
        setError(null);
    }, []);
    /**
     * Check if UAUI is ready
     */
    const isReady = (0, react_1.useCallback)(() => {
        return uaui_integration_service_1.default.isReady();
    }, []);
    /**
     * Get UAUI configuration
     */
    const getConfig = (0, react_1.useCallback)(() => {
        return uaui_integration_service_1.default.getConfig();
    }, []);
    // Auto-initialize on mount
    (0, react_1.useEffect)(() => {
        if (autoInitialize && !initializationAttempted.current && Array.isArray(providers) && providers.length > 0) {
            initializationAttempted.current = true;
            initialize();
        }
    }, [autoInitialize, initialize, providers]);
    // Listen for provider changes
    (0, react_1.useEffect)(() => {
        if (isInitialized && Array.isArray(providers) && providers.length > 0) {
            // Reinitialize when providers change
            const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];
            if (activeProviders.length !== availableProviders.length) {
                console.log('🔄 AI providers changed, reinitializing UAUI...');
                reinitialize();
            }
        }
    }, [providers, isInitialized, availableProviders, reinitialize]);
    // Cleanup on unmount
    (0, react_1.useEffect)(() => {
        return () => {
            if (isInitialized) {
                shutdown();
            }
        };
    }, [isInitialized, shutdown]);
    return {
        // State
        isInitialized,
        isLoading,
        error,
        availableProviders,
        // Actions
        initialize,
        processChatMessage,
        reinitialize,
        shutdown,
        clearError,
        // Utilities
        isReady,
        getConfig
    };
}
/**
 * Hook for simple UAUI chat processing
 */
function useUAUIChat(widgetId) {
    const uaui = useUAUI();
    const sendMessage = (0, react_1.useCallback)(async (message, userId, sessionId) => {
        const chatMessage = {
            id: `msg-${Date.now()}`,
            message,
            widgetId,
            userId,
            sessionId
        };
        return await uaui.processChatMessage(chatMessage);
    }, [uaui, widgetId]);
    return {
        ...uaui,
        sendMessage
    };
}
exports.default = useUAUI;
//# sourceMappingURL=use-uaui.js.map
/**
 * Simple Logger Utility for UAUI Protocol
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';
export interface LoggerConfig {
    level: LogLevel;
    prefix?: string;
    timestamp?: boolean;
}
export declare class Logger {
    private config;
    private static levels;
    constructor(config?: LoggerConfig);
    private shouldLog;
    private formatMessage;
    debug(message: string, ...args: any[]): void;
    info(message: string, ...args: any[]): void;
    warn(message: string, ...args: any[]): void;
    error(message: string, ...args: any[]): void;
    setLevel(level: LogLevel): void;
    getLevel(): LogLevel;
}
export declare const logger: Logger;
export declare function createLogger(config?: LoggerConfig): Logger;
//# sourceMappingURL=logger.d.ts.map
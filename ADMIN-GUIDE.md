# UAUI Protocol - System Administrator Guide

## Overview

This guide provides comprehensive instructions for deploying, securing, monitoring, and maintaining UAUI Protocol in production environments. It covers enterprise-grade deployment scenarios, security configurations, and operational procedures.

## Table of Contents
- [System Requirements](#system-requirements)
- [Deployment Architecture](#deployment-architecture)
- [Installation & Configuration](#installation--configuration)
- [Security Configuration](#security-configuration)
- [Monitoring & Logging](#monitoring--logging)
- [Backup & Recovery](#backup--recovery)
- [Performance Tuning](#performance-tuning)
- [Maintenance Procedures](#maintenance-procedures)
- [Troubleshooting](#troubleshooting)

## System Requirements

### Minimum Requirements
- **Node.js**: 16.0+ (LTS recommended)
- **Memory**: 2GB RAM minimum, 4GB recommended
- **Storage**: 10GB available space
- **Network**: Stable internet connection with HTTPS support
- **OS**: Linux (Ubuntu 20.04+), Windows Server 2019+, macOS 10.15+

### Recommended Production Environment
- **Node.js**: 18.x LTS
- **Memory**: 8GB RAM
- **Storage**: 50GB SSD
- **CPU**: 4+ cores
- **Network**: Load balancer with SSL termination
- **OS**: Ubuntu 22.04 LTS or RHEL 8+

### Network Requirements
- **Outbound HTTPS (443)**: Access to AI provider APIs
  - api.openai.com
  - api.groq.com
  - openrouter.ai
  - api.anthropic.com
  - generativelanguage.googleapis.com
- **Inbound HTTP/HTTPS**: Application access
- **DNS**: Reliable DNS resolution

## Deployment Architecture

### Single Instance Deployment
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Load Balancer │────│   UAUI Instance  │────│   AI Providers  │
│   (nginx/ALB)   │    │   (Node.js App)  │    │   (External)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                       ┌──────────────────┐
                       │   Monitoring     │
                       │   (Logs/Metrics) │
                       └──────────────────┘
```

### High Availability Deployment
```
                    ┌─────────────────┐
                    │  Load Balancer  │
                    └─────────┬───────┘
                              │
              ┌───────────────┼───────────────┐
              │               │               │
    ┌─────────▼─────┐ ┌───────▼─────┐ ┌───────▼─────┐
    │ UAUI Instance │ │ UAUI Instance│ │ UAUI Instance│
    │      #1       │ │      #2     │ │      #3     │
    └───────────────┘ └─────────────┘ └─────────────┘
              │               │               │
              └───────────────┼───────────────┘
                              │
                    ┌─────────▼─────────┐
                    │  Shared Storage   │
                    │  (Config/Logs)    │
                    └───────────────────┘
```

## Installation & Configuration

### Production Installation

#### 1. System Preparation
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18.x LTS
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 for process management
sudo npm install -g pm2

# Create application user
sudo useradd -m -s /bin/bash uaui
sudo usermod -aG sudo uaui
```

#### 2. Application Deployment
```bash
# Switch to application user
sudo su - uaui

# Create application directory
mkdir -p /opt/uaui
cd /opt/uaui

# Install UAUI Protocol
npm init -y
npm install uaui-protocol

# Create application structure
mkdir -p {config,logs,scripts,ssl}
```

#### 3. Configuration Files

**Production Configuration** (`config/production.json`):
```json
{
  "environment": "production",
  "logLevel": "info",
  "server": {
    "port": 3000,
    "host": "0.0.0.0",
    "ssl": {
      "enabled": true,
      "cert": "/opt/uaui/ssl/cert.pem",
      "key": "/opt/uaui/ssl/key.pem"
    }
  },
  "uaui": {
    "selectionStrategy": "smart",
    "maxConcurrentRequests": 100,
    "defaultTimeout": 30000,
    "caching": {
      "enabled": true,
      "ttl": 300000,
      "maxSize": 1000
    }
  },
  "monitoring": {
    "enabled": true,
    "metricsPort": 9090,
    "healthCheckPath": "/health"
  },
  "security": {
    "rateLimiting": {
      "enabled": true,
      "windowMs": 900000,
      "max": 1000
    },
    "cors": {
      "enabled": true,
      "origins": ["https://yourdomain.com"]
    }
  }
}
```

**Environment Variables** (`.env`):
```bash
NODE_ENV=production
PORT=3000

# AI Provider API Keys (use secrets management in production)
OPENAI_API_KEY=sk-your-openai-key
GROQ_API_KEY=gsk_your-groq-key
CLAUDE_API_KEY=sk-ant-your-claude-key
OPENROUTER_API_KEY=sk-or-your-openrouter-key

# Security
JWT_SECRET=your-jwt-secret-key
ENCRYPTION_KEY=your-encryption-key

# Monitoring
LOG_LEVEL=info
METRICS_ENABLED=true
```

#### 4. Application Startup Script

**PM2 Ecosystem** (`ecosystem.config.js`):
```javascript
module.exports = {
  apps: [{
    name: 'uaui-protocol',
    script: './app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=2048'
  }]
};
```

**Main Application** (`app.js`):
```javascript
const express = require('express');
const { createUAUIFromExisting } = require('uaui-protocol');
const config = require('./config/production.json');

const app = express();

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'healthy', timestamp: new Date().toISOString() });
});

// Initialize UAUI
const providers = [
  {
    type: 'openai',
    apiKey: process.env.OPENAI_API_KEY,
    defaultModel: 'gpt-4'
  },
  {
    type: 'groq',
    apiKey: process.env.GROQ_API_KEY,
    defaultModel: 'llama3-70b-8192'
  }
];

const uaui = createUAUIFromExisting(providers, config.uaui);

// API endpoints
app.post('/api/uaui/request', async (req, res) => {
  try {
    const response = await uaui.processAIRequest(req.body);
    res.json(response);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Start server
const port = process.env.PORT || 3000;
app.listen(port, () => {
  console.log(`UAUI server running on port ${port}`);
});

// Initialize UAUI
uaui.initialize().catch(console.error);
```

#### 5. Service Management
```bash
# Start application with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u uaui --hp /home/<USER>

# Enable PM2 service
sudo systemctl enable pm2-uaui
```

## Security Configuration

### SSL/TLS Configuration

#### 1. Certificate Management
```bash
# Using Let's Encrypt (recommended)
sudo apt install certbot
sudo certbot certonly --standalone -d yourdomain.com

# Copy certificates to application directory
sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem /opt/uaui/ssl/cert.pem
sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem /opt/uaui/ssl/key.pem
sudo chown uaui:uaui /opt/uaui/ssl/*
sudo chmod 600 /opt/uaui/ssl/*
```

#### 2. Nginx Reverse Proxy
```nginx
# /etc/nginx/sites-available/uaui
server {
    listen 80;
    server_name yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    
    # SSL Security Headers
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    
    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    
    location / {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        proxy_pass http://localhost:3000/health;
    }
}
```

### API Key Management

#### 1. Secrets Management with HashiCorp Vault
```bash
# Install Vault
wget https://releases.hashicorp.com/vault/1.15.0/vault_1.15.0_linux_amd64.zip
unzip vault_1.15.0_linux_amd64.zip
sudo mv vault /usr/local/bin/

# Initialize Vault
vault server -dev &
export VAULT_ADDR='http://127.0.0.1:8200'
vault auth -method=userpass

# Store API keys
vault kv put secret/uaui \
  openai_key="sk-your-openai-key" \
  groq_key="gsk_your-groq-key" \
  claude_key="sk-ant-your-claude-key"
```

#### 2. Environment-based Secrets
```bash
# Create secure environment file
sudo touch /opt/uaui/.env.production
sudo chmod 600 /opt/uaui/.env.production
sudo chown uaui:uaui /opt/uaui/.env.production

# Use systemd environment file
sudo systemctl edit pm2-uaui
```

Add to override file:
```ini
[Service]
EnvironmentFile=/opt/uaui/.env.production
```

### Firewall Configuration
```bash
# Configure UFW firewall
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH (adjust port as needed)
sudo ufw allow 22/tcp

# Allow HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow application port (if not using reverse proxy)
sudo ufw allow 3000/tcp

# Allow monitoring port (restrict to monitoring network)
sudo ufw allow from 10.0.0.0/8 to any port 9090
```

## Monitoring & Logging

### Application Monitoring

#### 1. Health Checks
```javascript
// Enhanced health check endpoint
app.get('/health', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    providers: {}
  };

  try {
    // Check UAUI status
    const providers = await uaui.getAvailableProviders();
    health.providers.available = providers;
    health.providers.count = providers.length;
    
    if (providers.length === 0) {
      health.status = 'degraded';
    }
  } catch (error) {
    health.status = 'unhealthy';
    health.error = error.message;
  }

  const statusCode = health.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(health);
});
```

#### 2. Metrics Collection
```javascript
// Prometheus metrics
const promClient = require('prom-client');

// Create metrics
const httpRequestDuration = new promClient.Histogram({
  name: 'http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status']
});

const uauiRequestsTotal = new promClient.Counter({
  name: 'uaui_requests_total',
  help: 'Total number of UAUI requests',
  labelNames: ['provider', 'status']
});

// Metrics endpoint
app.get('/metrics', (req, res) => {
  res.set('Content-Type', promClient.register.contentType);
  res.end(promClient.register.metrics());
});
```

### Log Management

#### 1. Structured Logging
```javascript
const winston = require('winston');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: { service: 'uaui-protocol' },
  transports: [
    new winston.transports.File({ 
      filename: '/opt/uaui/logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: '/opt/uaui/logs/combined.log' 
    })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.simple()
  }));
}
```

#### 2. Log Rotation
```bash
# Install logrotate configuration
sudo tee /etc/logrotate.d/uaui << EOF
/opt/uaui/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 uaui uaui
    postrotate
        pm2 reloadLogs
    endscript
}
EOF
```

### External Monitoring

#### 1. Prometheus Configuration
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'uaui-protocol'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

#### 2. Grafana Dashboard
```json
{
  "dashboard": {
    "title": "UAUI Protocol Monitoring",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(uaui_requests_total[5m])",
            "legendFormat": "{{provider}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      }
    ]
  }
}
```

## Backup & Recovery

### Configuration Backup
```bash
#!/bin/bash
# backup-config.sh

BACKUP_DIR="/opt/backups/uaui"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup configuration
tar -czf $BACKUP_DIR/config_$DATE.tar.gz \
  /opt/uaui/config/ \
  /opt/uaui/.env.production \
  /opt/uaui/ecosystem.config.js

# Backup SSL certificates
tar -czf $BACKUP_DIR/ssl_$DATE.tar.gz /opt/uaui/ssl/

# Cleanup old backups (keep 30 days)
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

### Disaster Recovery Plan

#### 1. Recovery Procedures
```bash
#!/bin/bash
# disaster-recovery.sh

# Stop services
pm2 stop all
sudo systemctl stop nginx

# Restore from backup
BACKUP_DATE=$1
tar -xzf /opt/backups/uaui/config_$BACKUP_DATE.tar.gz -C /
tar -xzf /opt/backups/uaui/ssl_$BACKUP_DATE.tar.gz -C /

# Restore permissions
sudo chown -R uaui:uaui /opt/uaui
sudo chmod 600 /opt/uaui/ssl/*

# Start services
sudo systemctl start nginx
pm2 start ecosystem.config.js

echo "Recovery completed from backup: $BACKUP_DATE"
```

#### 2. Automated Backup Schedule
```bash
# Add to crontab
sudo crontab -e

# Daily backup at 2 AM
0 2 * * * /opt/uaui/scripts/backup-config.sh

# Weekly full system backup
0 3 * * 0 /opt/uaui/scripts/full-backup.sh
```

## Performance Tuning

### Node.js Optimization
```bash
# Optimize Node.js settings
export NODE_OPTIONS="--max-old-space-size=4096 --optimize-for-size"

# PM2 cluster configuration
pm2 start ecosystem.config.js --instances max
pm2 set pm2:autodump true
```

### Database Connection Pooling
```javascript
// Connection pool for external services
const https = require('https');

const agent = new https.Agent({
  keepAlive: true,
  maxSockets: 50,
  maxFreeSockets: 10,
  timeout: 60000,
  freeSocketTimeout: 30000
});

// Use agent for all HTTPS requests
const fetchWithAgent = (url, options = {}) => {
  return fetch(url, { ...options, agent });
};
```

### Caching Strategy
```javascript
// Redis caching implementation
const redis = require('redis');
const client = redis.createClient({
  host: 'localhost',
  port: 6379,
  retry_strategy: (options) => {
    if (options.error && options.error.code === 'ECONNREFUSED') {
      return new Error('Redis server connection refused');
    }
    if (options.total_retry_time > 1000 * 60 * 60) {
      return new Error('Retry time exhausted');
    }
    return Math.min(options.attempt * 100, 3000);
  }
});

// Cache middleware
const cacheMiddleware = (ttl = 300) => {
  return async (req, res, next) => {
    const key = `uaui:${req.method}:${req.originalUrl}`;

    try {
      const cached = await client.get(key);
      if (cached) {
        return res.json(JSON.parse(cached));
      }
    } catch (error) {
      console.warn('Cache read error:', error);
    }

    // Override res.json to cache response
    const originalJson = res.json;
    res.json = function(data) {
      client.setex(key, ttl, JSON.stringify(data));
      return originalJson.call(this, data);
    };

    next();
  };
};
```

### Load Balancing
```nginx
# Nginx upstream configuration
upstream uaui_backend {
    least_conn;
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3001 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3002 max_fails=3 fail_timeout=30s;
    keepalive 32;
}

server {
    location / {
        proxy_pass http://uaui_backend;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Connection pooling
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
}
```

## Maintenance Procedures

### Regular Maintenance Tasks

#### Daily Tasks
```bash
#!/bin/bash
# daily-maintenance.sh

# Check system health
curl -f http://localhost:3000/health || echo "Health check failed"

# Check disk space
df -h | awk '$5 > 80 {print "Warning: " $0}'

# Check memory usage
free -m | awk 'NR==2{printf "Memory Usage: %s/%sMB (%.2f%%)\n", $3,$2,$3*100/$2 }'

# Check PM2 processes
pm2 status

# Rotate logs if needed
sudo logrotate -f /etc/logrotate.d/uaui

# Check SSL certificate expiry
openssl x509 -in /opt/uaui/ssl/cert.pem -noout -dates
```

#### Weekly Tasks
```bash
#!/bin/bash
# weekly-maintenance.sh

# Update system packages
sudo apt update && sudo apt list --upgradable

# Check for Node.js updates
npm outdated -g

# Analyze log files
tail -1000 /opt/uaui/logs/error.log | grep -i error | wc -l

# Performance analysis
pm2 monit --no-daemon &
sleep 30
pkill -f "pm2 monit"

# Security scan
sudo lynis audit system --quick
```

#### Monthly Tasks
```bash
#!/bin/bash
# monthly-maintenance.sh

# Full system backup
/opt/uaui/scripts/full-backup.sh

# Update dependencies
cd /opt/uaui
npm audit
npm update

# SSL certificate renewal
sudo certbot renew --dry-run

# Performance report
echo "Generating monthly performance report..."
# Add custom reporting logic here
```

### Update Procedures

#### UAUI Protocol Updates
```bash
#!/bin/bash
# update-uaui.sh

# Backup current installation
/opt/uaui/scripts/backup-config.sh

# Stop application
pm2 stop uaui-protocol

# Update UAUI package
cd /opt/uaui
npm update uaui-protocol

# Run tests
npm test

# Start application
pm2 start uaui-protocol

# Verify health
sleep 10
curl -f http://localhost:3000/health

echo "UAUI update completed"
```

#### System Updates
```bash
#!/bin/bash
# system-update.sh

# Create system snapshot (if using LVM/cloud snapshots)
# aws ec2 create-snapshot --volume-id vol-xxxxx

# Update packages
sudo apt update
sudo apt upgrade -y

# Update Node.js if needed
# nvm install --lts
# nvm use --lts

# Restart services
sudo systemctl restart nginx
pm2 restart all

# Verify services
systemctl status nginx
pm2 status

echo "System update completed"
```

## Troubleshooting

### Common Issues

#### High Memory Usage
```bash
# Check memory usage by process
ps aux --sort=-%mem | head -10

# Check Node.js heap usage
node -e "console.log(process.memoryUsage())"

# PM2 memory monitoring
pm2 monit

# Solutions:
# 1. Increase max-old-space-size
# 2. Enable garbage collection logging
# 3. Implement memory leak detection
```

#### High CPU Usage
```bash
# Check CPU usage
top -p $(pgrep -d',' node)

# Check PM2 CPU usage
pm2 show uaui-protocol

# Profile application
node --prof app.js
# Generate profile report
node --prof-process isolate-*.log > profile.txt

# Solutions:
# 1. Optimize code paths
# 2. Implement request queuing
# 3. Scale horizontally
```

#### Network Issues
```bash
# Test AI provider connectivity
curl -I https://api.openai.com
curl -I https://api.groq.com

# Check DNS resolution
nslookup api.openai.com

# Test SSL connectivity
openssl s_client -connect api.openai.com:443

# Check firewall rules
sudo ufw status verbose

# Solutions:
# 1. Check firewall rules
# 2. Verify DNS settings
# 3. Test with different network
```

#### Application Crashes
```bash
# Check PM2 logs
pm2 logs uaui-protocol --lines 100

# Check system logs
sudo journalctl -u pm2-uaui -f

# Check for core dumps
ls -la /var/crash/

# Enable core dumps
echo 'kernel.core_pattern = /var/crash/core.%e.%p.%t' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# Solutions:
# 1. Analyze error logs
# 2. Check memory limits
# 3. Verify dependencies
```

### Diagnostic Tools

#### Health Check Script
```bash
#!/bin/bash
# health-check.sh

echo "=== UAUI System Health Check ==="

# Check services
echo "1. Service Status:"
systemctl is-active nginx
pm2 status | grep uaui-protocol

# Check connectivity
echo "2. Network Connectivity:"
curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/health

# Check resources
echo "3. System Resources:"
df -h /opt/uaui
free -m | grep Mem

# Check logs for errors
echo "4. Recent Errors:"
tail -50 /opt/uaui/logs/error.log | grep -c ERROR

# Check SSL certificate
echo "5. SSL Certificate:"
openssl x509 -in /opt/uaui/ssl/cert.pem -noout -enddate

echo "=== Health Check Complete ==="
```

#### Performance Analysis
```bash
#!/bin/bash
# performance-analysis.sh

echo "=== Performance Analysis ==="

# Response time test
echo "1. Response Time Test:"
time curl -s http://localhost:3000/health > /dev/null

# Load test
echo "2. Load Test (100 requests):"
ab -n 100 -c 10 http://localhost:3000/health

# Memory analysis
echo "3. Memory Analysis:"
ps -o pid,ppid,cmd,%mem,%cpu --sort=-%mem | head -10

# Disk I/O
echo "4. Disk I/O:"
iostat -x 1 3

echo "=== Analysis Complete ==="
```

### Emergency Procedures

#### Service Recovery
```bash
#!/bin/bash
# emergency-recovery.sh

echo "Starting emergency recovery..."

# Stop all services
pm2 stop all
sudo systemctl stop nginx

# Kill any hanging processes
pkill -f node
pkill -f nginx

# Clear temporary files
rm -rf /tmp/pm2*
rm -rf /opt/uaui/logs/*.log

# Restart services
sudo systemctl start nginx
pm2 start ecosystem.config.js

# Wait and verify
sleep 30
curl -f http://localhost:3000/health

echo "Emergency recovery completed"
```

#### Rollback Procedure
```bash
#!/bin/bash
# rollback.sh

BACKUP_DATE=$1

if [ -z "$BACKUP_DATE" ]; then
    echo "Usage: $0 <backup_date>"
    exit 1
fi

echo "Rolling back to backup: $BACKUP_DATE"

# Stop services
pm2 stop all

# Restore configuration
tar -xzf /opt/backups/uaui/config_$BACKUP_DATE.tar.gz -C /

# Restore permissions
chown -R uaui:uaui /opt/uaui

# Start services
pm2 start ecosystem.config.js

echo "Rollback completed"
```

## Security Hardening

### System Hardening
```bash
# Disable unnecessary services
sudo systemctl disable bluetooth
sudo systemctl disable cups

# Configure fail2ban
sudo apt install fail2ban
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# SSH hardening
sudo sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sudo systemctl restart ssh

# File permissions
sudo chmod 700 /opt/uaui
sudo chmod 600 /opt/uaui/.env*
sudo chmod 600 /opt/uaui/ssl/*
```

### Application Security
```javascript
// Security middleware
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"]
    }
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});

app.use('/api/', limiter);
```

## Compliance & Auditing

### Audit Logging
```javascript
// Audit middleware
const auditLog = (req, res, next) => {
  const audit = {
    timestamp: new Date().toISOString(),
    ip: req.ip,
    method: req.method,
    url: req.originalUrl,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id
  };

  logger.info('API_ACCESS', audit);
  next();
};

app.use(auditLog);
```

### Compliance Checklist
- [ ] Data encryption at rest and in transit
- [ ] Access logging and monitoring
- [ ] Regular security updates
- [ ] Backup and recovery procedures
- [ ] Incident response plan
- [ ] User access controls
- [ ] API key rotation
- [ ] Security scanning

## Support & Escalation

### Support Levels
1. **Level 1**: Basic troubleshooting, service restarts
2. **Level 2**: Configuration issues, performance problems
3. **Level 3**: Code-level issues, architecture problems

### Escalation Procedures
1. **Check documentation** and common issues
2. **Gather diagnostic information** (logs, metrics, configs)
3. **Contact development team** with detailed information
4. **Engage vendor support** for critical issues

### Emergency Contacts
- **System Administrator**: [contact info]
- **Development Team Lead**: [contact info]
- **Security Team**: [contact info]
- **Vendor Support**: [contact info]

---

**Related Documentation:**
- [Developer Guide](./DEVELOPER-GUIDE.md) - Technical implementation
- [Business Guide](./BUSINESS-GUIDE.md) - ROI and business case
- [User Manual](./USER-MANUAL.md) - End user instructions
- [Quick Start Guide](./QUICK-START.md) - Rapid deployment

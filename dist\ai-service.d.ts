/**
 * Universal AI Service
 * Integrates with multiple AI providers and provides intelligent selection
 */
import { <PERSON><PERSON>onfig, AIProvider, AIProviderType, UAUIRequest, RequestContext, ResponseMetadata } from './types';
import { Logger } from './utils/logger';
export interface AIServiceResponse {
    message: string;
    actions?: any[];
    data?: any;
    metadata: ResponseMetadata;
}
export declare class UniversalAIService {
    private config;
    private logger;
    private providers;
    private selectionStrategy;
    private isInitialized;
    constructor(config: AIConfig, logger: Logger);
    initialize(): Promise<void>;
    process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse>;
    getProvider(providerId: string): Promise<AIProviderAdapter | undefined>;
    getAvailableProviders(): Promise<AIProviderAdapter[]>;
    testProvider(providerId: string): Promise<boolean>;
    shutdown(): Promise<void>;
    private createProviderAdapter;
    private createSelectionStrategy;
    private processFallback;
    private updateProviderMetrics;
}
export declare abstract class AIProviderAdapter {
    protected provider: <PERSON>Provider;
    protected logger: Logger;
    constructor(provider: <PERSON><PERSON>rovider, logger: Logger);
    abstract initialize(): Promise<void>;
    abstract process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse>;
    abstract test(): Promise<boolean>;
    abstract isAvailable(): Promise<boolean>;
    abstract shutdown(): Promise<void>;
    getId(): string;
    getName(): string;
    getType(): AIProviderType;
    /**
     * Generate actions from AI response text
     */
    protected generateActionsFromResponse(responseText: string, request: UAUIRequest): any[];
}
export declare class OpenAIAdapter extends AIProviderAdapter {
    private client;
    initialize(): Promise<void>;
    process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse>;
    test(): Promise<boolean>;
    isAvailable(): Promise<boolean>;
    shutdown(): Promise<void>;
    private callOpenAI;
}
export declare class ClaudeAdapter extends AIProviderAdapter {
    initialize(): Promise<void>;
    process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse>;
    test(): Promise<boolean>;
    isAvailable(): Promise<boolean>;
    shutdown(): Promise<void>;
    private callClaude;
}
export declare class GeminiAdapter extends AIProviderAdapter {
    initialize(): Promise<void>;
    process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse>;
    test(): Promise<boolean>;
    isAvailable(): Promise<boolean>;
    shutdown(): Promise<void>;
    private callGemini;
}
export declare class MistralAdapter extends AIProviderAdapter {
    initialize(): Promise<void>;
    process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse>;
    test(): Promise<boolean>;
    isAvailable(): Promise<boolean>;
    shutdown(): Promise<void>;
    private callMistral;
}
export declare class GroqAdapter extends AIProviderAdapter {
    initialize(): Promise<void>;
    process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse>;
    test(): Promise<boolean>;
    isAvailable(): Promise<boolean>;
    shutdown(): Promise<void>;
    private callGroq;
}
export declare class OpenRouterAdapter extends AIProviderAdapter {
    initialize(): Promise<void>;
    process(request: UAUIRequest, context: RequestContext): Promise<AIServiceResponse>;
    test(): Promise<boolean>;
    isAvailable(): Promise<boolean>;
    shutdown(): Promise<void>;
    private callOpenRouter;
}
export declare abstract class ProviderSelectionStrategy {
    protected logger: Logger;
    constructor(logger: Logger);
    abstract select(request: UAUIRequest, context: RequestContext, providers: Map<string, AIProviderAdapter>): Promise<AIProviderAdapter>;
}
export declare class SmartSelectionStrategy extends ProviderSelectionStrategy {
    select(request: UAUIRequest, context: RequestContext, providers: Map<string, AIProviderAdapter>): Promise<AIProviderAdapter>;
    private findProvider;
    private getFirstAvailable;
}
export declare class RoundRobinStrategy extends ProviderSelectionStrategy {
    private currentIndex;
    select(request: UAUIRequest, context: RequestContext, providers: Map<string, AIProviderAdapter>): Promise<AIProviderAdapter>;
}
export declare class LeastLatencyStrategy extends ProviderSelectionStrategy {
    private performanceMetrics;
    select(request: UAUIRequest, context: RequestContext, providers: Map<string, AIProviderAdapter>): Promise<AIProviderAdapter>;
    updateMetrics(providerId: string, latency: number): void;
}
//# sourceMappingURL=ai-service.d.ts.map
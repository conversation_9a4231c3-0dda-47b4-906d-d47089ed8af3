/**
 * UAUI Event Bus
 * High-performance event system for cross-app communication
 */

import { EventEmitter } from 'events';
import { UAUIEvent, UAUIEventType, EventMetadata } from './types';
import { Logger } from './utils/logger';

export type EventListener = (event: UAUIEvent) => void | Promise<void>;
export type EventMiddleware = (event: UAUIEvent, next: () => void) => void | Promise<void>;

export class EventBus {
  private nodeEmitter: EventEmitter;
  private logger: Logger;
  private eventListeners = new Map<UAUIEventType, Set<EventListener>>();
  private middleware: EventMiddleware[] = [];
  private eventHistory: UAUIEvent[] = [];
  private maxHistorySize = 1000;
  private isInitialized = false;

  constructor(logger: Logger) {
    this.nodeEmitter = new EventEmitter();
    this.logger = logger;
    this.nodeEmitter.setMaxListeners(100); // Increase default limit
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    this.logger.info('Initializing Event Bus...');
    this.setupErrorHandling();
    this.isInitialized = true;
    this.logger.info('Event Bus initialized');
  }

  /**
   * Emit an event through the bus
   */
  emit(event: UAUIEvent): boolean {
    try {
      // Validate event
      this.validateEvent(event);

      // Add to history
      this.addToHistory(event);

      // Apply middleware
      this.applyMiddleware(event, () => {
        // Route to listeners
        this.routeToListeners(event);

        // Handle cross-app routing
        if (event.metadata?.crossApp) {
          this.routeCrossApp(event);
        }

        // Emit on EventEmitter for compatibility
        this.nodeEmitter.emit(event.type, event);
        this.nodeEmitter.emit('*', event); // Wildcard listener
      });

      this.logger.debug(`Event emitted: ${event.type}`, { id: event.id });
      return true;

    } catch (error) {
      this.logger.error('Failed to emit event:', error);
      this.emitError(event, error);
      return false;
    }
  }

  /**
   * Subscribe to events
   */
  on(eventType: UAUIEventType | '*', listener: EventListener): () => void {
    if (eventType === '*') {
      // Wildcard listener
      this.nodeEmitter.on('*', listener);
      return () => this.nodeEmitter.off('*', listener);
    }

    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set());
    }

    this.eventListeners.get(eventType)!.add(listener);

    this.logger.debug(`Listener added for event: ${eventType}`);

    // Return unsubscribe function
    return () => {
      this.eventListeners.get(eventType)?.delete(listener);
      if (this.eventListeners.get(eventType)?.size === 0) {
        this.eventListeners.delete(eventType);
      }
    };
  }

  /**
   * Subscribe to events once
   */
  once(eventType: UAUIEventType, listener: EventListener): () => void {
    const onceListener = (event: UAUIEvent) => {
      listener(event);
      unsubscribe();
    };

    const unsubscribe = this.on(eventType, onceListener);
    return unsubscribe;
  }

  /**
   * Remove all listeners for an event type
   */
  off(eventType: UAUIEventType): void {
    this.eventListeners.delete(eventType);
    this.nodeEmitter.removeAllListeners(eventType);
    this.logger.debug(`All listeners removed for event: ${eventType}`);
  }

  /**
   * Add middleware to the event pipeline
   */
  use(middleware: EventMiddleware): void {
    this.middleware.push(middleware);
    this.logger.debug('Middleware added to event bus');
  }

  /**
   * Get event history
   */
  getHistory(filter?: {
    type?: UAUIEventType;
    source?: string;
    target?: string;
    since?: number;
    limit?: number;
  }): UAUIEvent[] {
    let filtered = [...this.eventHistory];

    if (filter) {
      if (filter.type) {
        filtered = filtered.filter(e => e.type === filter.type);
      }
      if (filter.source) {
        filtered = filtered.filter(e => e.source.app === filter.source);
      }
      if (filter.target) {
        filtered = filtered.filter(e => e.target?.app === filter.target);
      }
      if (filter.since) {
        filtered = filtered.filter(e => e.timestamp >= filter.since!);
      }
      if (filter.limit) {
        filtered = filtered.slice(-filter.limit);
      }
    }

    return filtered;
  }

  /**
   * Clear event history
   */
  clearHistory(): void {
    this.eventHistory = [];
    this.logger.debug('Event history cleared');
  }

  /**
   * Get listener count for an event type
   */
  listenerCount(eventType: UAUIEventType): number {
    return this.eventListeners.get(eventType)?.size || 0;
  }

  /**
   * Get all event types with listeners
   */
  getEventTypes(): UAUIEventType[] {
    return Array.from(this.eventListeners.keys());
  }

  /**
   * Shutdown the event bus
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down Event Bus...');

    this.eventListeners.clear();
    this.middleware = [];
    this.eventHistory = [];
    this.nodeEmitter.removeAllListeners();

    this.isInitialized = false;
    this.logger.info('Event Bus shut down');
  }

  // Private methods

  private validateEvent(event: UAUIEvent): void {
    if (!event.id || !event.type || !event.timestamp || !event.source) {
      throw new Error('Invalid event: missing required fields');
    }

    if (typeof event.timestamp !== 'number' || event.timestamp <= 0) {
      throw new Error('Invalid event: invalid timestamp');
    }
  }

  private addToHistory(event: UAUIEvent): void {
    this.eventHistory.push(event);

    // Trim history if it exceeds max size
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory = this.eventHistory.slice(-this.maxHistorySize);
    }
  }

  private applyMiddleware(event: UAUIEvent, next: () => void): void {
    let index = 0;

    const runMiddleware = () => {
      if (index >= this.middleware.length) {
        next();
        return;
      }

      const middleware = this.middleware[index++];
      try {
        middleware(event, runMiddleware);
      } catch (error) {
        this.logger.error('Middleware error:', error);
        this.emitError(event, error);
      }
    };

    runMiddleware();
  }

  private routeToListeners(event: UAUIEvent): void {
    const listeners = this.eventListeners.get(event.type);
    if (!listeners || listeners.size === 0) {
      return;
    }

    for (const listener of listeners) {
      try {
        const result = listener(event);

        // Handle async listeners
        if (result instanceof Promise) {
          result.catch(error => {
            this.logger.error('Async listener error:', error);
            this.emitError(event, error);
          });
        }
      } catch (error) {
        this.logger.error('Listener error:', error);
        this.emitError(event, error);
      }
    }
  }

  private routeCrossApp(event: UAUIEvent): void {
    if (!event.target?.app) {
      this.logger.warn('Cross-app event without target app:', event.id);
      return;
    }

    // Emit cross-app routing event
    this.nodeEmitter.emit('cross_app_route', {
      ...event,
      type: 'cross_app.route',
      id: `route-${event.id}`,
      timestamp: Date.now()
    });
  }

  private setupErrorHandling(): void {
    this.on('error' as UAUIEventType, (event) => {
      this.logger.error('Event bus error:', event.data);
    });

    // Handle uncaught errors
    process.on('uncaughtException', (error) => {
      this.logger.error('Uncaught exception in event bus:', error);
    });

    process.on('unhandledRejection', (reason) => {
      this.logger.error('Unhandled rejection in event bus:', reason);
    });
  }

  private emitError(originalEvent: UAUIEvent, error: any): void {
    const errorEvent: UAUIEvent = {
      id: `error-${Date.now()}`,
      type: 'system.error',
      timestamp: Date.now(),
      source: { app: 'uaui-event-bus' },
      data: {
        originalEvent,
        error: {
          message: error.message,
          stack: error.stack,
          timestamp: Date.now()
        }
      },
      metadata: {
        priority: 'high'
      }
    };

    // Emit error event (but don't recurse if this fails)
    try {
      this.nodeEmitter.emit('error', errorEvent);
    } catch (emitError) {
      this.logger.error('Failed to emit error event:', emitError);
    }
  }
}

// ============================================================================
// BUILT-IN MIDDLEWARE
// ============================================================================

/**
 * Logging middleware
 */
export const loggingMiddleware = (logger: Logger): EventMiddleware => {
  return (event: UAUIEvent, next: () => void) => {
    logger.debug(`Event: ${event.type}`, {
      id: event.id,
      source: event.source.app,
      target: event.target?.app
    });
    next();
  };
};

/**
 * Rate limiting middleware
 */
export const rateLimitMiddleware = (
  maxEvents: number = 100,
  windowMs: number = 60000
): EventMiddleware => {
  const eventCounts = new Map<string, { count: number; resetTime: number }>();

  return (event: UAUIEvent, next: () => void) => {
    const key = `${event.source.app}:${event.type}`;
    const now = Date.now();

    let bucket = eventCounts.get(key);
    if (!bucket || now > bucket.resetTime) {
      bucket = { count: 0, resetTime: now + windowMs };
      eventCounts.set(key, bucket);
    }

    if (bucket.count >= maxEvents) {
      throw new Error(`Rate limit exceeded for ${key}`);
    }

    bucket.count++;
    next();
  };
};

/**
 * Validation middleware
 */
export const validationMiddleware = (): EventMiddleware => {
  return (event: UAUIEvent, next: () => void) => {
    // Additional validation beyond basic checks
    if (event.metadata?.priority && !['low', 'normal', 'high', 'critical'].includes(event.metadata.priority)) {
      throw new Error('Invalid priority level');
    }

    if (event.metadata?.ttl && event.metadata.ttl <= 0) {
      throw new Error('Invalid TTL value');
    }

    // Validate event structure
    if (!event.id || typeof event.id !== 'string') {
      throw new Error('Event must have a valid string ID');
    }

    if (!event.type || typeof event.type !== 'string') {
      throw new Error('Event must have a valid string type');
    }

    if (!event.source || !event.source.app) {
      throw new Error('Event must have a valid source app');
    }

    next();
  };
};

/**
 * Metrics middleware
 */
export const metricsMiddleware = (): EventMiddleware => {
  const metrics = {
    totalEvents: 0,
    eventsByType: new Map<string, number>(),
    eventsByApp: new Map<string, number>()
  };

  return (event: UAUIEvent, next: () => void) => {
    metrics.totalEvents++;

    const typeCount = metrics.eventsByType.get(event.type) || 0;
    metrics.eventsByType.set(event.type, typeCount + 1);

    const appCount = metrics.eventsByApp.get(event.source.app) || 0;
    metrics.eventsByApp.set(event.source.app, appCount + 1);

    // Attach metrics to global object for monitoring
    (global as any).uauiMetrics = metrics;

    next();
  };
};

"use strict";
/**
 * UAUI Module Index
 * Centralized exports for the Universal AI User Interface integration
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UAUIWidgetAssistant = exports.UAUINavigationGuide = exports.UAUINotificationBanner = exports.UAUIFloatingButton = exports.UAUIProviderConfig = exports.UAUIStatusCard = exports.uauiIntegrationService = exports.useUAUI = void 0;
// Hooks
var use_uaui_1 = require("./hooks/use-uaui");
Object.defineProperty(exports, "useUAUI", { enumerable: true, get: function () { return use_uaui_1.useUAUI; } });
// Services
var uaui_integration_service_1 = require("./services/uaui-integration-service");
Object.defineProperty(exports, "uauiIntegrationService", { enumerable: true, get: function () { return __importDefault(uaui_integration_service_1).default; } });
// Components (re-export from components/uaui)
var uaui_1 = require("../../components/uaui");
Object.defineProperty(exports, "UAUIStatusCard", { enumerable: true, get: function () { return uaui_1.UAUIStatusCard; } });
Object.defineProperty(exports, "UAUIProviderConfig", { enumerable: true, get: function () { return uaui_1.UAUIProviderConfig; } });
Object.defineProperty(exports, "UAUIFloatingButton", { enumerable: true, get: function () { return uaui_1.UAUIFloatingButton; } });
Object.defineProperty(exports, "UAUINotificationBanner", { enumerable: true, get: function () { return uaui_1.UAUINotificationBanner; } });
Object.defineProperty(exports, "UAUINavigationGuide", { enumerable: true, get: function () { return uaui_1.UAUINavigationGuide; } });
Object.defineProperty(exports, "UAUIWidgetAssistant", { enumerable: true, get: function () { return uaui_1.UAUIWidgetAssistant; } });
//# sourceMappingURL=index.js.map
# UAUI Protocol Environment Configuration
# Copy this file to .env and add your real API keys

# OpenAI Configuration (Required)
OPENAI_API_KEY=sk-your-openai-api-key-here
# Get your key from: https://platform.openai.com/api-keys

# Groq Configuration (Required) 
GROQ_API_KEY=gsk_your-groq-api-key-here
# Get your key from: https://console.groq.com/keys

# OpenRouter Configuration (Required)
OPENROUTER_API_KEY=sk-or-your-openrouter-api-key-here
# Get your key from: https://openrouter.ai/keys

# Optional: Claude Configuration (for future use)
# CLAUDE_API_KEY=sk-ant-your-claude-api-key-here
# Get your key from: https://console.anthropic.com/

# Optional: Gemini Configuration (for future use)
# GEMINI_API_KEY=your-gemini-api-key-here
# Get your key from: https://makersuite.google.com/app/apikey

# Optional: Mistral Configuration (for future use)
# MISTRAL_API_KEY=your-mistral-api-key-here
# Get your key from: https://console.mistral.ai/

# UAUI Configuration
UAUI_ENVIRONMENT=development
UAUI_LOG_LEVEL=info
UAUI_SELECTION_STRATEGY=smart

/**
 * Universal AI-UI Protocol (UAUI) - Core Engine
 * Production-ready, independent protocol implementation
 */

import { EventEmitter } from 'events';
import {
  UAUIConfig,
  UAUIEvent,
  UAUIRequest,
  UAUIResponse,
  UAUIAction,
  CrossAppAction,
  UAUIAppConfig,
  UAUIError,
  AIProvider,
  RequestContext,
  ResponseMetadata
} from './types';
import { EventBus } from './event-bus';
import { StateManager } from './state-manager';
import { AppRouter } from './app-router';
import { UniversalAIService } from './ai-service';
import { Logger } from './utils/logger';

export class UAUICore extends EventEmitter {
  private config: UAUIConfig;
  private eventBus: EventBus;
  private stateManager: StateManager;
  private appRouter: AppRouter;
  private aiService: UniversalAIService;
  private logger: Logger;
  private apps = new Map<string, UAUIApp>();
  private isInitialized = false;

  constructor(config: UAUIConfig) {
    super();
    this.config = config;
    this.logger = new Logger({ level: config.core.logLevel });
    this.eventBus = new EventBus(this.logger);
    this.stateManager = new StateManager(this.logger);
    this.appRouter = new AppRouter(this.logger);
    this.aiService = new UniversalAIService(config.ai, this.logger);
  }

  /**
   * Initialize the UAUI core engine
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      throw new Error('UAUI Core already initialized');
    }

    try {
      this.logger.info('Initializing UAUI Core Engine...');

      // Initialize components
      await this.eventBus.initialize();
      await this.stateManager.initialize();
      await this.appRouter.initialize();
      await this.aiService.initialize();

      // Setup event handlers
      this.setupEventHandlers();

      // Register configured apps
      for (const [appId, appConfig] of Object.entries(this.config.apps)) {
        await this.registerApp(appId, appConfig);
      }

      this.isInitialized = true;
      this.logger.info('UAUI Core Engine initialized successfully');
      this.emit('initialized');
    } catch (error) {
      this.logger.error('Failed to initialize UAUI Core:', error);
      throw error;
    }
  }

  /**
   * Register a new application with the UAUI protocol
   */
  async registerApp(appId: string, config: UAUIAppConfig): Promise<UAUIApp> {
    if (this.apps.has(appId)) {
      throw new Error(`App ${appId} already registered`);
    }

    const app = new UAUIApp(appId, config, this);
    await app.initialize();

    this.apps.set(appId, app);
    this.appRouter.registerApp(appId, app);

    this.logger.info(`Registered app: ${appId}`);
    this.emit('app_registered', { appId, config });

    return app;
  }

  /**
   * Process an AI request through the UAUI protocol
   */
  async processAIRequest(request: UAUIRequest): Promise<UAUIResponse> {
    const startTime = Date.now();

    try {
      this.logger.debug('Processing AI request:', request.id);

      // Validate request
      this.validateRequest(request);

      // Enrich context
      const enrichedContext = await this.enrichContext(request.context);

      // Process with AI service
      const aiResponse = await this.aiService.process(request, enrichedContext);

      // Plan and execute actions
      const actions = await this.planActions(aiResponse, enrichedContext);
      const crossAppAction = actions.find(a => a.type.startsWith('cross_app.'));

      // Execute cross-app actions if any
      if (crossAppAction) {
        await this.executeCrossAppAction({
          targetApp: crossAppAction.target,
          action: crossAppAction,
          context: enrichedContext
        });
      }

      // Build response
      const response: UAUIResponse = {
        id: `response-${Date.now()}`,
        requestId: request.id,
        message: aiResponse.message,
        actions,
        crossAppAction: crossAppAction ? {
          targetApp: crossAppAction.target,
          action: crossAppAction,
          context: enrichedContext
        } : undefined,
        data: aiResponse.data,
        metadata: {
          ...aiResponse.metadata,
          responseTime: Date.now() - startTime,
          timestamp: Date.now()
        }
      };

      // Emit response event
      this.eventBus.emit({
        id: `event-${Date.now()}`,
        type: 'ai.response',
        timestamp: Date.now(),
        source: { app: 'uaui-core' },
        target: { app: request.context.app },
        data: response
      });

      this.logger.debug('AI request processed successfully:', request.id);
      return response;

    } catch (error) {
      this.logger.error('Failed to process AI request:', error);

      const errorResponse: UAUIResponse = {
        id: `error-${Date.now()}`,
        requestId: request.id,
        metadata: {
          provider: 'error',
          model: 'error',
          responseTime: Date.now() - startTime,
          timestamp: Date.now()
        },
        error: {
          code: 'INTERNAL_ERROR',
          message: error instanceof Error ? error.message : 'Unknown error',
          timestamp: Date.now(),
          recoverable: true
        }
      };

      return errorResponse;
    }
  }

  /**
   * Synchronize state between applications
   */
  async syncState(fromApp: string, toApp: string, state: any): Promise<void> {
    try {
      await this.stateManager.sync(fromApp, toApp, state);

      this.eventBus.emit({
        id: `sync-${Date.now()}`,
        type: 'state.sync',
        timestamp: Date.now(),
        source: { app: fromApp },
        target: { app: toApp },
        data: state,
        metadata: { crossApp: true }
      });

      this.logger.debug(`State synced from ${fromApp} to ${toApp}`);
    } catch (error) {
      this.logger.error('Failed to sync state:', error);
      throw error;
    }
  }

  /**
   * Execute a cross-application action
   */
  async executeCrossAppAction(crossAppAction: CrossAppAction): Promise<void> {
    try {
      const targetApp = this.apps.get(crossAppAction.targetApp);
      if (!targetApp) {
        throw new Error(`Target app not found: ${crossAppAction.targetApp}`);
      }

      await this.appRouter.routeAction(crossAppAction);

      this.eventBus.emit({
        id: `cross-app-${Date.now()}`,
        type: 'cross_app.action',
        timestamp: Date.now(),
        source: { app: 'uaui-core' },
        target: { app: crossAppAction.targetApp },
        data: crossAppAction,
        metadata: { crossApp: true }
      });

      this.logger.debug(`Cross-app action executed: ${crossAppAction.targetApp}`);
    } catch (error) {
      this.logger.error('Failed to execute cross-app action:', error);
      throw error;
    }
  }

  /**
   * Get application instance
   */
  getApp(appId: string): UAUIApp | undefined {
    return this.apps.get(appId);
  }

  /**
   * Get all registered applications
   */
  getApps(): Map<string, UAUIApp> {
    return new Map(this.apps);
  }

  /**
   * Get UAUI configuration
   */
  getConfig(): UAUIConfig {
    return { ...this.config };
  }

  /**
   * Get AI service instance
   */
  getAIService(): UniversalAIService {
    return this.aiService;
  }

  /**
   * Get event bus instance
   */
  getEventBus(): EventBus {
    return this.eventBus;
  }

  /**
   * Shutdown the UAUI core engine
   */
  async shutdown(): Promise<void> {
    try {
      this.logger.info('Shutting down UAUI Core Engine...');

      // Shutdown all apps
      for (const app of this.apps.values()) {
        await app.shutdown();
      }

      // Shutdown components
      await this.aiService.shutdown();
      await this.appRouter.shutdown();
      await this.stateManager.shutdown();
      await this.eventBus.shutdown();

      this.isInitialized = false;
      this.logger.info('UAUI Core Engine shut down successfully');
      this.emit('shutdown');
    } catch (error) {
      this.logger.error('Error during shutdown:', error);
      throw error;
    }
  }

  // Private methods

  private setupEventHandlers(): void {
    this.eventBus.on('ai.request', this.handleAIRequest.bind(this));
    this.eventBus.on('cross_app.action', this.handleCrossAppAction.bind(this));
    this.eventBus.on('state.sync', this.handleStateSync.bind(this));
  }

  private async handleAIRequest(event: UAUIEvent): Promise<void> {
    const request = event.data as UAUIRequest;
    await this.processAIRequest(request);
  }

  private async handleCrossAppAction(event: UAUIEvent): Promise<void> {
    const action = event.data as CrossAppAction;
    await this.executeCrossAppAction(action);
  }

  private async handleStateSync(event: UAUIEvent): Promise<void> {
    const { fromApp, toApp, state } = event.data;
    await this.syncState(fromApp, toApp, state);
  }

  private validateRequest(request: UAUIRequest): void {
    if (!request.id || !request.type || !request.context) {
      throw new Error('Invalid UAUI request: missing required fields');
    }
  }

  private async enrichContext(context: RequestContext): Promise<RequestContext> {
    // Add cross-app context, user history, preferences, etc.
    const enriched = { ...context };

    // Add session information
    if (!enriched.session) {
      enriched.session = {
        id: `session-${Date.now()}`,
        startTime: Date.now(),
        lastActivity: Date.now(),
        interactions: 0
      };
    }

    // Add system context
    enriched.system = {
      timestamp: Date.now(),
      version: this.config.core.version,
      environment: this.config.core.environment
    };

    return enriched;
  }

  private async planActions(aiResponse: any, context: RequestContext): Promise<UAUIAction[]> {
    // This would contain sophisticated action planning logic
    // For now, return actions from AI response
    return aiResponse.actions || [];
  }
}

/**
 * UAUI Application wrapper
 */
export class UAUIApp {
  private id: string;
  private config: UAUIAppConfig;
  private core: UAUICore;
  private isInitialized = false;

  constructor(id: string, config: UAUIAppConfig, core: UAUICore) {
    this.id = id;
    this.config = config;
    this.core = core;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    // Setup app-specific event handlers
    for (const handler of this.config.eventHandlers) {
      this.core.getEventBus().on(handler.eventType, this.handleEvent.bind(this));
    }

    this.isInitialized = true;
  }

  async shutdown(): Promise<void> {
    this.isInitialized = false;
  }

  getId(): string {
    return this.id;
  }

  getConfig(): UAUIAppConfig {
    return { ...this.config };
  }

  private async handleEvent(event: UAUIEvent): Promise<void> {
    // Handle app-specific events
    console.log(`App ${this.id} handling event:`, event.type);
  }
}

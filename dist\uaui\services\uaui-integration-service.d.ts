/**
 * UAUI Integration Service for Tempo Widget
 * Bridges Tempo Widget's AI providers with the UAUI system
 */
import type { UAUIResponse } from '../..';
export interface ChatMessage {
    id: string;
    message: string;
    context?: any;
    metadata?: Record<string, any>;
}
export interface UAUIActionResult {
    success: boolean;
    data?: any;
    error?: string;
}
interface TempoUAUIConfig {
    environment?: 'development' | 'production';
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
    selectionStrategy?: 'smart' | 'round-robin' | 'random';
}
/**
 * UAUI Integration Service
 * Manages UAUI lifecycle and integration with Tempo Widget system
 */
declare class UAUIIntegrationService {
    private uaui;
    private isInitialized;
    private config;
    constructor(config?: TempoUAUIConfig);
    /**
     * Initialize UAUI with current AI providers from Tempo Widget
     */
    initialize(): Promise<boolean>;
    /**
     * Reinitialize UAUI with updated providers
     */
    reinitialize(): Promise<boolean>;
    /**
     * Shutdown UAUI
     */
    shutdown(): Promise<void>;
    /**
     * Send message to UAUI
     */
    sendMessage(message: ChatMessage): Promise<UAUIResponse | null>;
    /**
     * Get available providers
     */
    getAvailableProviders(): string[];
    /**
     * Check if UAUI is ready
     */
    isReady(): boolean;
    /**
     * Get UAUI status
     */
    getStatus(): {
        initialized: boolean;
        hasInstance: boolean;
        providers: string[];
        config: TempoUAUIConfig;
    };
    /**
     * Update configuration
     */
    updateConfig(newConfig: Partial<TempoUAUIConfig>): void;
}
declare const uauiIntegrationService: UAUIIntegrationService;
export default uauiIntegrationService;
export { UAUIIntegrationService };
export type { TempoUAUIConfig, ChatMessage, UAUIActionResult };
//# sourceMappingURL=uaui-integration-service.d.ts.map
"use strict";
/**
 * UAUI Protocol - Universal AI User Interface
 * Standalone Open Source AI Provider Management System
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PROTOCOL_VERSION = exports.VERSION = exports.createUAUIFromExisting = exports.UAUI = void 0;
exports.getVersion = getVersion;
exports.getProtocolVersion = getProtocolVersion;
exports.isCompatible = isCompatible;
// Core exports
var uaui_1 = require("./uaui");
Object.defineProperty(exports, "UAUI", { enumerable: true, get: function () { return uaui_1.UAUI; } });
Object.defineProperty(exports, "createUAUIFromExisting", { enumerable: true, get: function () { return uaui_1.createUAUIFromExisting; } });
// Version
exports.VERSION = '1.0.0';
exports.PROTOCOL_VERSION = '1.0';
// Utility functions
function getVersion() {
    return exports.VERSION;
}
function getProtocolVersion() {
    return exports.PROTOCOL_VERSION;
}
function isCompatible(version) {
    const [major] = version.split('.');
    const [currentMajor] = exports.PROTOCOL_VERSION.split('.');
    return major === currentMajor;
}
//# sourceMappingURL=main.js.map
/**
 * UAUI Factory
 * Simplified creation and configuration of UAUI instances
 */

import { UAUI<PERSON>ore } from './core';
import { UAUIConfig, AIProvider, UAUIAppConfig } from './types';
import { DEFAULT_CONFIG } from './index';

export class UAUIFactory {
  /**
   * Create a UAUI instance with minimal configuration
   */
  static create(options: UAUIFactoryOptions = {}): UAUICore {
    const config = this.buildConfig(options);
    return new UAUICore(config);
  }

  /**
   * Create a UAUI instance for widget applications
   */
  static createForWidget(options: WidgetFactoryOptions): UAUICore {
    const config = this.buildConfig({
      ...options,
      apps: {
        widget: {
          id: 'widget',
          name: 'Widget Application',
          type: 'widget',
          version: '1.0.0',
          capabilities: [
            { type: 'chat', version: '1.0.0' },
            { type: 'ui_control', version: '1.0.0' }
          ],
          eventHandlers: [
            { eventType: 'ai.request', handler: 'handleAIRequest' },
            { eventType: 'ui.update', handler: 'handleUIUpdate' }
          ],
          crossAppEnabled: true
        }
      }
    });

    return new UAUICore(config);
  }

  /**
   * Create a UAUI instance for dashboard applications
   */
  static createForDashboard(options: DashboardFactoryOptions): UAUICore {
    const config = this.buildConfig({
      ...options,
      apps: {
        dashboard: {
          id: 'dashboard',
          name: 'Dashboard Application',
          type: 'dashboard',
          version: '1.0.0',
          capabilities: [
            { type: 'analytics', version: '1.0.0' },
            { type: 'visualization', version: '1.0.0' }
          ],
          eventHandlers: [
            { eventType: 'ai.request', handler: 'handleAIRequest' },
            { eventType: 'cross_app.action', handler: 'handleCrossAppAction' }
          ],
          crossAppEnabled: true
        }
      }
    });

    return new UAUICore(config);
  }

  /**
   * Create AI providers from your existing configuration
   */
  static createAIProviders(providers: ExistingProviderConfig[]): AIProvider[] {
    return providers.map(provider => ({
      id: provider.id || `${provider.type}-${Date.now()}`,
      name: provider.name || provider.type.toUpperCase(),
      type: provider.type as 'openai' | 'claude' | 'gemini' | 'mistral' | 'groq' | 'openrouter',
      config: {
        apiKey: provider.apiKey,
        baseUrl: provider.baseUrl,
        models: provider.models || this.getDefaultModels(provider.type),
        defaultModel: provider.defaultModel || this.getDefaultModels(provider.type)[0],
        maxTokens: provider.maxTokens || 4000,
        temperature: provider.temperature || 0.7,
        timeout: provider.timeout || 30000,
        retries: provider.retries || 3
      },
      capabilities: this.getProviderCapabilities(provider.type),
      status: {
        available: true,
        lastChecked: Date.now(),
        latency: 0,
        errorRate: 0
      },
      metrics: {
        totalRequests: 0,
        successRate: 1.0,
        averageLatency: 0,
        totalTokens: 0,
        totalCost: 0,
        lastUsed: 0
      }
    }));
  }

  // Private helper methods

  private static buildConfig(options: UAUIFactoryOptions): UAUIConfig {
    return {
      core: {
        ...DEFAULT_CONFIG.core,
        environment: (options.environment as 'development' | 'staging' | 'production') || DEFAULT_CONFIG.core.environment,
        logLevel: (options.logLevel as 'debug' | 'info' | 'warn' | 'error') || DEFAULT_CONFIG.core.logLevel
      },
      ai: {
        ...DEFAULT_CONFIG.ai,
        providers: options.aiProviders || [],
        selectionStrategy: (options.selectionStrategy as 'round_robin' | 'least_latency' | 'smart' | 'custom') || DEFAULT_CONFIG.ai.selectionStrategy
      },
      apps: options.apps || {},
      transport: options.transport,
      security: options.security,
      monitoring: options.monitoring
    };
  }

  private static getDefaultModels(type: string): string[] {
    const modelMap: Record<string, string[]> = {
      openai: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
      claude: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
      gemini: ['gemini-pro', 'gemini-ultra'],
      mistral: ['mistral-large', 'mistral-medium', 'mistral-small'],
      groq: ['llama3-70b-8192', 'llama3-8b-8192', 'mixtral-8x7b-32768'],
      openrouter: ['openai/gpt-4', 'anthropic/claude-3-opus', 'google/gemini-pro', 'meta-llama/llama-3-70b']
    };
    return modelMap[type] || ['default-model'];
  }

  private static getProviderCapabilities(type: string): any[] {
    const capabilityMap: Record<string, any[]> = {
      openai: [
        { type: 'chat', models: ['gpt-4', 'gpt-4-turbo'], streaming: true },
        { type: 'completion', models: ['gpt-3.5-turbo'], streaming: true },
        { type: 'code', models: ['gpt-4'], streaming: true }
      ],
      claude: [
        { type: 'chat', models: ['claude-3-opus', 'claude-3-sonnet'], streaming: true },
        { type: 'analysis', models: ['claude-3-opus'], streaming: true }
      ],
      gemini: [
        { type: 'chat', models: ['gemini-pro'], streaming: true },
        { type: 'image', models: ['gemini-pro-vision'], streaming: false }
      ],
      mistral: [
        { type: 'chat', models: ['mistral-large'], streaming: true },
        { type: 'completion', models: ['mistral-medium'], streaming: true }
      ],
      groq: [
        { type: 'chat', models: ['llama3-70b-8192'], streaming: true },
        { type: 'completion', models: ['mixtral-8x7b-32768'], streaming: true }
      ],
      openrouter: [
        { type: 'chat', models: ['openai/gpt-4', 'anthropic/claude-3-opus'], streaming: true },
        { type: 'completion', models: ['google/gemini-pro'], streaming: true },
        { type: 'multimodel', models: ['meta-llama/llama-3-70b'], streaming: true }
      ]
    };
    return capabilityMap[type] || [];
  }
}

// ============================================================================
// FACTORY TYPES
// ============================================================================

export interface UAUIFactoryOptions {
  environment?: 'development' | 'staging' | 'production';
  logLevel?: 'debug' | 'info' | 'warn' | 'error';
  aiProviders?: AIProvider[];
  selectionStrategy?: 'round_robin' | 'least_latency' | 'smart' | 'custom';
  apps?: Record<string, UAUIAppConfig>;
  transport?: any;
  security?: any;
  monitoring?: any;
}

export interface WidgetFactoryOptions extends UAUIFactoryOptions {
  widgetConfig?: {
    appearance?: any;
    behavior?: any;
    aiSettings?: any;
  };
}

export interface DashboardFactoryOptions extends UAUIFactoryOptions {
  dashboardConfig?: {
    analytics?: any;
    visualization?: any;
  };
}

export interface ExistingProviderConfig {
  id?: string;
  name?: string;
  type: 'openai' | 'claude' | 'gemini' | 'mistral' | 'groq' | 'openrouter';
  apiKey: string;
  baseUrl?: string;
  models?: string[];
  defaultModel?: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
  retries?: number;
}

// ============================================================================
// CONVENIENCE FUNCTIONS
// ============================================================================

/**
 * Quick setup for widget applications
 */
export function createWidgetUAUI(providers: ExistingProviderConfig[]): UAUICore {
  const aiProviders = UAUIFactory.createAIProviders(providers);
  return UAUIFactory.createForWidget({ aiProviders });
}

/**
 * Quick setup for dashboard applications
 */
export function createDashboardUAUI(providers: ExistingProviderConfig[]): UAUICore {
  const aiProviders = UAUIFactory.createAIProviders(providers);
  return UAUIFactory.createForDashboard({ aiProviders });
}

/**
 * Quick setup with your existing AI provider configuration
 */
export function createUAUIFromExisting(
  providers: ExistingProviderConfig[],
  options: UAUIFactoryOptions = {}
): UAUICore {
  const aiProviders = UAUIFactory.createAIProviders(providers);
  return UAUIFactory.create({ ...options, aiProviders });
}

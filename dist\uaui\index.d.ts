/**
 * UAUI Module Index
 * Centralized exports for the Universal AI User Interface integration
 */
export { useUAUI } from './hooks/use-uaui';
export { default as uauiIntegrationService } from './services/uaui-integration-service';
export type { ChatMessage, UAUIActionResult, TempoUAUIConfig } from './services/uaui-integration-service';
export { UAUIStatusCard, UAUIProviderConfig, UAUIFloatingButton, UAUINotificationBanner, UAUINavigationGuide, UAUIWidgetAssistant } from '../../components/uaui';
export type { UseUAUIOptions, UseUAUIReturn } from './hooks/use-uaui';
//# sourceMappingURL=index.d.ts.map
/**
 * UAUI App Router
 * Routes actions and events between applications
 */

import { UA<PERSON>A<PERSON>, CrossAppAction, UAUIEvent } from './types';
import { Logger } from './utils/logger';
import { UAUIApp } from './core';

export interface RouteConfig {
  source: string;
  target: string;
  actionTypes: string[];
  transform?: (action: UAUIAction) => UAUIAction;
  condition?: (action: UAUIAction) => boolean;
}

export class AppRouter {
  private logger: Logger;
  private apps = new Map<string, UAUIApp>();
  private routes = new Map<string, RouteConfig[]>();
  private actionQueue: QueuedAction[] = [];
  private isProcessing = false;
  private isInitialized = false;

  constructor(logger: Logger) {
    this.logger = logger;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    this.logger.info('Initializing App Router...');
    this.startActionProcessor();
    this.isInitialized = true;
    this.logger.info('App Router initialized');
  }

  /**
   * Register an application with the router
   */
  registerApp(appId: string, app: UAUIApp): void {
    this.apps.set(appId, app);
    this.logger.debug(`App registered with router: ${appId}`);
  }

  /**
   * Unregister an application
   */
  unregisterApp(appId: string): void {
    this.apps.delete(appId);
    this.routes.delete(appId);
    this.logger.debug(`App unregistered from router: ${appId}`);
  }

  /**
   * Add a routing rule
   */
  addRoute(config: RouteConfig): void {
    if (!this.routes.has(config.source)) {
      this.routes.set(config.source, []);
    }

    this.routes.get(config.source)!.push(config);
    this.logger.debug(`Route added: ${config.source} -> ${config.target}`);
  }

  /**
   * Add multiple routing rules
   */
  addRoutes(configs: RouteConfig[]): void {
    configs.forEach(config => this.addRoute(config));
  }

  /**
   * Get routing rules for a source app
   */
  getRoutes(sourceApp: string): RouteConfig[] {
    return this.routes.get(sourceApp) || [];
  }

  /**
   * Remove routing rules for a source app
   */
  removeRoutes(sourceApp: string): void {
    this.routes.delete(sourceApp);
    this.logger.debug(`Routes removed for app: ${sourceApp}`);
  }

  /**
   * Route a cross-app action
   */
  async routeAction(crossAppAction: CrossAppAction): Promise<void> {
    try {
      const targetApp = this.apps.get(crossAppAction.targetApp);
      if (!targetApp) {
        throw new Error(`Target app not found: ${crossAppAction.targetApp}`);
      }

      // Add to queue for processing
      this.actionQueue.push({
        crossAppAction,
        timestamp: Date.now(),
        retries: 0
      });

      this.logger.debug(`Action queued for routing: ${crossAppAction.action.type} -> ${crossAppAction.targetApp}`);

    } catch (error) {
      this.logger.error('Failed to route action:', error);
      throw error;
    }
  }

  /**
   * Route an event to target applications
   */
  async routeEvent(event: UAUIEvent): Promise<void> {
    try {
      if (!event.target?.app) {
        // Broadcast to all apps if no specific target
        await this.broadcastEvent(event);
        return;
      }

      const targetApp = this.apps.get(event.target.app);
      if (!targetApp) {
        this.logger.warn(`Target app not found for event: ${event.target.app}`);
        return;
      }

      await this.deliverEventToApp(event, targetApp);
      this.logger.debug(`Event routed: ${event.type} -> ${event.target.app}`);

    } catch (error) {
      this.logger.error('Failed to route event:', error);
      throw error;
    }
  }

  /**
   * Get routing statistics
   */
  getStats(): RouterStats {
    return {
      registeredApps: this.apps.size,
      activeRoutes: Array.from(this.routes.values()).reduce((sum, routes) => sum + routes.length, 0),
      queuedActions: this.actionQueue.length,
      isProcessing: this.isProcessing
    };
  }

  /**
   * Get all registered applications
   */
  getRegisteredApps(): string[] {
    return Array.from(this.apps.keys());
  }

  /**
   * Check if an app is registered
   */
  isAppRegistered(appId: string): boolean {
    return this.apps.has(appId);
  }

  /**
   * Shutdown the router
   */
  async shutdown(): Promise<void> {
    this.logger.info('Shutting down App Router...');

    this.isProcessing = false;
    this.actionQueue = [];
    this.apps.clear();
    this.routes.clear();

    this.isInitialized = false;
    this.logger.info('App Router shut down');
  }

  // Private methods

  private startActionProcessor(): void {
    this.isProcessing = true;
    this.processActionQueue();
  }

  private async processActionQueue(): Promise<void> {
    while (this.isProcessing) {
      if (this.actionQueue.length === 0) {
        await this.sleep(100); // Wait 100ms before checking again
        continue;
      }

      const queuedAction = this.actionQueue.shift()!;

      try {
        await this.executeAction(queuedAction);
      } catch (error) {
        await this.handleActionError(queuedAction, error);
      }
    }
  }

  private async executeAction(queuedAction: QueuedAction): Promise<void> {
    const { crossAppAction } = queuedAction;
    const targetApp = this.apps.get(crossAppAction.targetApp);

    if (!targetApp) {
      throw new Error(`Target app not found: ${crossAppAction.targetApp}`);
    }

    // Apply routing transformations
    const transformedAction = await this.applyRouteTransforms(crossAppAction);

    // Execute the action on the target app
    await this.executeActionOnApp(transformedAction, targetApp);

    this.logger.debug(`Action executed: ${transformedAction.action.type} on ${crossAppAction.targetApp}`);
  }

  private async applyRouteTransforms(crossAppAction: CrossAppAction): Promise<CrossAppAction> {
    const sourceApp = crossAppAction.context?.app || 'unknown';
    const routes = this.routes.get(sourceApp) || [];

    let transformedAction = { ...crossAppAction };

    for (const route of routes) {
      if (route.target === crossAppAction.targetApp &&
        route.actionTypes.includes(crossAppAction.action.type)) {

        // Check condition if specified
        if (route.condition && !route.condition(crossAppAction.action)) {
          continue;
        }

        // Apply transformation if specified
        if (route.transform) {
          transformedAction.action = route.transform(transformedAction.action);
        }
      }
    }

    return transformedAction;
  }

  private async executeActionOnApp(crossAppAction: CrossAppAction, targetApp: UAUIApp): Promise<void> {
    // This would call the target app's action handler
    // For now, we'll just log the action
    this.logger.info(`Executing action on app ${targetApp.getId()}:`, {
      type: crossAppAction.action.type,
      target: crossAppAction.action.target,
      payload: crossAppAction.action.payload
    });

    // In a real implementation, this would:
    // 1. Call the app's action handler
    // 2. Update the app's state
    // 3. Trigger any side effects
    // 4. Return results
  }

  private async handleActionError(queuedAction: QueuedAction, error: any): Promise<void> {
    queuedAction.retries++;

    if (queuedAction.retries < 3) {
      // Retry the action
      this.actionQueue.unshift(queuedAction);
      this.logger.warn(`Action failed, retrying (${queuedAction.retries}/3):`, error.message);
    } else {
      // Max retries reached, log error and discard
      this.logger.error(`Action failed after 3 retries, discarding:`, {
        action: queuedAction.crossAppAction.action.type,
        target: queuedAction.crossAppAction.targetApp,
        error: error.message
      });
    }
  }

  private async broadcastEvent(event: UAUIEvent): Promise<void> {
    const promises = Array.from(this.apps.values()).map(app =>
      this.deliverEventToApp(event, app)
    );

    await Promise.allSettled(promises);
    this.logger.debug(`Event broadcasted: ${event.type} to ${this.apps.size} apps`);
  }

  private async deliverEventToApp(event: UAUIEvent, app: UAUIApp): Promise<void> {
    try {
      // This would call the app's event handler
      // For now, we'll just log the delivery
      this.logger.debug(`Delivering event to app ${app.getId()}:`, {
        type: event.type,
        id: event.id
      });

      // In a real implementation, this would:
      // 1. Call the app's event handler
      // 2. Handle any responses
      // 3. Update routing metrics
    } catch (error) {
      this.logger.error(`Failed to deliver event to app ${app.getId()}:`, error);
      throw error;
    }
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// ============================================================================
// TYPES
// ============================================================================

interface QueuedAction {
  crossAppAction: CrossAppAction;
  timestamp: number;
  retries: number;
}

export interface RouterStats {
  registeredApps: number;
  activeRoutes: number;
  queuedActions: number;
  isProcessing: boolean;
}

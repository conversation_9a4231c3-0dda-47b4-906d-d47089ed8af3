{"version": 3, "file": "state-manager.d.ts", "sourceRoot": "", "sources": ["../src/state-manager.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AAExC,MAAM,WAAW,aAAa;IAC5B,EAAE,EAAE,MAAM,CAAC;IACX,KAAK,EAAE,MAAM,CAAC;IACd,SAAS,EAAE,MAAM,CAAC;IAClB,KAAK,EAAE,GAAG,CAAC;IACX,OAAO,EAAE,MAAM,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,gBAAgB;IAC/B,SAAS,CAAC,EAAE,OAAO,CAAC;IACpB,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;IACrB,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK,GAAG,CAAC;CACjC;AAED,qBAAa,YAAY;IACvB,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,SAAS,CAA0B;IAC3C,OAAO,CAAC,YAAY,CAAsC;IAC1D,OAAO,CAAC,iBAAiB,CAA6C;IACtE,OAAO,CAAC,cAAc,CAAM;IAC5B,OAAO,CAAC,aAAa,CAAS;gBAElB,MAAM,EAAE,MAAM;IAIpB,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAQjC;;OAEG;IACG,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC;IAsBpF;;OAEG;IACH,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,GAAG;IAI5B;;OAEG;IACH,eAAe,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,aAAa,EAAE;IAK/D;;OAEG;IACG,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,EAAE,GAAG,EAAE,OAAO,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC;IA8BlG;;OAEG;IACH,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,iBAAiB,GAAG,MAAM,IAAI;IAkBjE;;OAEG;IACG,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE,QAAQ,GAAE,QAAQ,GAAG,YAAY,GAAG,QAAmB,GAAG,OAAO,CAAC,GAAG,CAAC;IAqB1G;;OAEG;IACG,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAYpE;;OAEG;IACG,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAQ9C;;OAEG;IACH,YAAY,IAAI,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAQnC;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;IAa/B,OAAO,CAAC,cAAc;IActB,OAAO,CAAC,YAAY;YAcN,iBAAiB;IAsB/B,OAAO,CAAC,aAAa;IAUrB,OAAO,CAAC,iBAAiB;IAKzB,OAAO,CAAC,aAAa;IAIrB,OAAO,CAAC,eAAe;IAavB,OAAO,CAAC,SAAS;IAwBjB,OAAO,CAAC,SAAS;IAuBjB,OAAO,CAAC,iBAAiB;CAa1B;AAMD,MAAM,WAAW,iBAAiB;IAChC,CAAC,KAAK,EAAE,gBAAgB,GAAG,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;CACjD;AAED,MAAM,WAAW,gBAAgB;IAC/B,KAAK,EAAE,MAAM,CAAC;IACd,QAAQ,EAAE,GAAG,CAAC;IACd,aAAa,EAAE,GAAG,CAAC;IACnB,SAAS,EAAE,MAAM,CAAC;CACnB"}
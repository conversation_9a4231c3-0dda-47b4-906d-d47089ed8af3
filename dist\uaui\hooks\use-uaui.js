"use strict";
/**
 * UAUI React Hook
 * Provides React integration for the Universal AI User Interface system
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useUAUI = useUAUI;
const react_1 = require("react");
const ai_provider_store_1 = require("@/stores/ai-provider-store");
const uaui_integration_service_1 = __importDefault(require("../services/uaui-integration-service"));
/**
 * React hook for UAUI integration
 */
function useUAUI(options = {}) {
    const { autoInitialize = true, environment = 'development', logLevel = 'info' } = options;
    // State
    const [isInitialized, setIsInitialized] = (0, react_1.useState)(false);
    const [isLoading, setIsLoading] = (0, react_1.useState)(false);
    const [error, setError] = (0, react_1.useState)(null);
    const [availableProviders, setAvailableProviders] = (0, react_1.useState)([]);
    // Refs
    const initializationAttempted = (0, react_1.useRef)(false);
    // AI Provider store
    const { providers, fetchProviders } = (0, ai_provider_store_1.useAIProviderStore)();
    /**
     * Initialize UAUI
     */
    const initialize = (0, react_1.useCallback)(async () => {
        if (isLoading)
            return false;
        setIsLoading(true);
        setError(null);
        try {
            console.log('🚀 Initializing UAUI system...');
            const success = await uaui_integration_service_1.default.initialize();
            if (success) {
                setIsInitialized(true);
                // Get available providers
                const providers = uaui_integration_service_1.default.getAvailableProviders();
                setAvailableProviders(providers);
                console.log('✅ UAUI initialized successfully');
                console.log(`📊 Available providers: ${providers.join(', ')}`);
            }
            else {
                setError('Failed to initialize UAUI system');
                console.error('❌ UAUI initialization failed');
            }
            return success;
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
            setError(errorMessage);
            console.error('❌ UAUI initialization error:', err);
            return false;
        }
        finally {
            setIsLoading(false);
        }
    }, [isLoading]);
    /**
     * Reinitialize UAUI
     */
    const reinitialize = (0, react_1.useCallback)(async () => {
        console.log('🔄 Reinitializing UAUI system...');
        try {
            await shutdown();
            return await initialize();
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Reinitialization failed';
            setError(errorMessage);
            console.error('❌ UAUI reinitialization error:', err);
            return false;
        }
    }, [initialize]);
    /**
     * Shutdown UAUI
     */
    const shutdown = (0, react_1.useCallback)(async () => {
        console.log('🛑 Shutting down UAUI system...');
        try {
            await uaui_integration_service_1.default.shutdown();
            setIsInitialized(false);
            setAvailableProviders([]);
            setError(null);
            console.log('✅ UAUI shutdown complete');
        }
        catch (err) {
            console.error('❌ UAUI shutdown error:', err);
        }
    }, []);
    /**
     * Clear error state
     */
    const clearError = (0, react_1.useCallback)(() => {
        setError(null);
    }, []);
    /**
     * Send message to UAUI
     */
    const sendMessage = (0, react_1.useCallback)(async (message) => {
        if (!isInitialized) {
            throw new Error('UAUI is not initialized');
        }
        try {
            const response = await uaui_integration_service_1.default.sendMessage(message);
            return response;
        }
        catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
            setError(errorMessage);
            console.error('❌ UAUI message error:', err);
            return null;
        }
    }, [isInitialized]);
    /**
     * Check if UAUI is ready
     */
    const isReady = (0, react_1.useCallback)(() => {
        return uaui_integration_service_1.default.isReady();
    }, []);
    /**
     * Get current status
     */
    const getStatus = (0, react_1.useCallback)(() => {
        return {
            initialized: isInitialized,
            loading: isLoading,
            error,
            providers: availableProviders
        };
    }, [isInitialized, isLoading, error, availableProviders]);
    // Auto-initialize on mount
    (0, react_1.useEffect)(() => {
        if (autoInitialize && !initializationAttempted.current && Array.isArray(providers) && providers.length > 0) {
            initializationAttempted.current = true;
            initialize();
        }
    }, [autoInitialize, initialize, providers]);
    // Listen for provider changes
    (0, react_1.useEffect)(() => {
        if (isInitialized && Array.isArray(providers) && providers.length > 0) {
            // Reinitialize when providers change
            const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];
            if (activeProviders.length !== availableProviders.length) {
                console.log('🔄 AI providers changed, reinitializing UAUI...');
                reinitialize();
            }
        }
    }, [providers, isInitialized, availableProviders, reinitialize]);
    // Cleanup on unmount
    (0, react_1.useEffect)(() => {
        return () => {
            if (isInitialized) {
                shutdown();
            }
        };
    }, [isInitialized, shutdown]);
    return {
        // State
        isInitialized,
        isLoading,
        error,
        availableProviders,
        // Actions
        initialize,
        reinitialize,
        shutdown,
        clearError,
        // Messaging
        sendMessage,
        // Utilities
        isReady,
        getStatus
    };
}
//# sourceMappingURL=use-uaui.js.map
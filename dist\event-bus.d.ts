/**
 * UAUI Event Bus
 * High-performance event system for cross-app communication
 */
import { UAUIEvent, UAUIEventType } from './types';
import { Logger } from './utils/logger';
export type EventListener = (event: UAUIEvent) => void | Promise<void>;
export type EventMiddleware = (event: UAUIEvent, next: () => void) => void | Promise<void>;
export declare class EventBus {
    private nodeEmitter;
    private logger;
    private eventListeners;
    private middleware;
    private eventHistory;
    private maxHistorySize;
    private isInitialized;
    constructor(logger: Logger);
    initialize(): Promise<void>;
    /**
     * Emit an event through the bus
     */
    emit(event: UAUIEvent): boolean;
    /**
     * Subscribe to events
     */
    on(eventType: UAUIEventType | '*', listener: EventListener): () => void;
    /**
     * Subscribe to events once
     */
    once(eventType: UAUIEventType, listener: EventListener): () => void;
    /**
     * Remove all listeners for an event type
     */
    off(eventType: UAUIEventType): void;
    /**
     * Add middleware to the event pipeline
     */
    use(middleware: EventMiddleware): void;
    /**
     * Get event history
     */
    getHistory(filter?: {
        type?: UAUIEventType;
        source?: string;
        target?: string;
        since?: number;
        limit?: number;
    }): UAUIEvent[];
    /**
     * Clear event history
     */
    clearHistory(): void;
    /**
     * Get listener count for an event type
     */
    listenerCount(eventType: UAUIEventType): number;
    /**
     * Get all event types with listeners
     */
    getEventTypes(): UAUIEventType[];
    /**
     * Shutdown the event bus
     */
    shutdown(): Promise<void>;
    private validateEvent;
    private addToHistory;
    private applyMiddleware;
    private routeToListeners;
    private routeCrossApp;
    private setupErrorHandling;
    private emitError;
}
/**
 * Logging middleware
 */
export declare const loggingMiddleware: (logger: Logger) => EventMiddleware;
/**
 * Rate limiting middleware
 */
export declare const rateLimitMiddleware: (maxEvents?: number, windowMs?: number) => EventMiddleware;
/**
 * Validation middleware
 */
export declare const validationMiddleware: () => EventMiddleware;
/**
 * Metrics middleware
 */
export declare const metricsMiddleware: () => EventMiddleware;
//# sourceMappingURL=event-bus.d.ts.map
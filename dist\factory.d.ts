/**
 * UAUI Factory
 * Simplified creation and configuration of UAUI instances
 */
import { UAUICore } from './core';
import { <PERSON><PERSON>rovider, UAUIAppConfig } from './types';
export declare class UAUIFactory {
    /**
     * Create a UAUI instance with minimal configuration
     */
    static create(options?: UAUIFactoryOptions): UAUICore;
    /**
     * Create a UAUI instance for widget applications
     */
    static createForWidget(options: WidgetFactoryOptions): UAUICore;
    /**
     * Create a UAUI instance for dashboard applications
     */
    static createForDashboard(options: DashboardFactoryOptions): UAUICore;
    /**
     * Create AI providers from your existing configuration
     */
    static createAIProviders(providers: ExistingProviderConfig[]): AIProvider[];
    private static buildConfig;
    private static getDefaultModels;
    private static getProviderCapabilities;
}
export interface UAUIFactoryOptions {
    environment?: 'development' | 'staging' | 'production';
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
    aiProviders?: AIProvider[];
    selectionStrategy?: 'round_robin' | 'least_latency' | 'smart' | 'custom';
    apps?: Record<string, UAUIAppConfig>;
    transport?: any;
    security?: any;
    monitoring?: any;
}
export interface WidgetFactoryOptions extends UAUIFactoryOptions {
    widgetConfig?: {
        appearance?: any;
        behavior?: any;
        aiSettings?: any;
    };
}
export interface DashboardFactoryOptions extends UAUIFactoryOptions {
    dashboardConfig?: {
        analytics?: any;
        visualization?: any;
    };
}
export interface ExistingProviderConfig {
    id?: string;
    name?: string;
    type: 'openai' | 'claude' | 'gemini' | 'mistral' | 'groq' | 'openrouter';
    apiKey: string;
    baseUrl?: string;
    models?: string[];
    defaultModel?: string;
    maxTokens?: number;
    temperature?: number;
    timeout?: number;
    retries?: number;
}
/**
 * Quick setup for widget applications
 */
export declare function createWidgetUAUI(providers: ExistingProviderConfig[]): UAUICore;
/**
 * Quick setup for dashboard applications
 */
export declare function createDashboardUAUI(providers: ExistingProviderConfig[]): UAUICore;
/**
 * Quick setup with your existing AI provider configuration
 */
export declare function createUAUIFromExisting(providers: ExistingProviderConfig[], options?: UAUIFactoryOptions): UAUICore;
//# sourceMappingURL=factory.d.ts.map
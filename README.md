# UAUI Protocol - Universal AI User Interface

**Open Source AI Provider Management System**

UAUI is a standalone, open-source protocol that enables intelligent AI provider selection, cross-app communication, and seamless user interface control through natural language commands.

## 🚀 Features

- **Smart AI Provider Selection** - Automatically chooses the best AI for each task
- **Production-Ready Providers** - OpenAI, Groq, OpenRouter implementations
- **Cross-App Communication** - State sync and action routing between applications
- **Event-Driven Architecture** - Scalable and reactive system design
- **TypeScript Support** - Complete type safety and IntelliSense
- **Zero Dependencies** - Lightweight and self-contained

## 📦 Installation

```bash
npm install uaui-protocol
```

## 🔧 Quick Start

```typescript
import { createUAUIFromExisting } from 'uaui-protocol';

// Configure your AI providers
const providers = [
  {
    type: 'openai',
    apiKey: process.env.OPENAI_API_KEY,
    defaultModel: 'gpt-4'
  },
  {
    type: 'groq', 
    apiKey: process.env.GROQ_API_KEY,
    defaultModel: 'llama3-70b-8192'
  },
  {
    type: 'openrouter',
    apiKey: process.env.OPENROUTER_API_KEY,
    defaultModel: 'openai/gpt-4'
  }
];

// Initialize UAUI
const uaui = createUAUIFromExisting(providers);
await uaui.initialize();

// Process AI requests with automatic provider selection
const response = await uaui.processAIRequest({
  id: 'request-1',
  message: 'Change the widget color to blue and show analytics',
  context: { app: 'my-app' }
});

// Response includes:
// - AI-generated text response
// - Automatic UI actions
// - Cross-app communication triggers
```

## 🎯 Smart Provider Selection

UAUI automatically selects the best AI provider based on the request:

- **Analysis tasks** → Claude (best for deep analysis)
- **Quick responses** → Groq (fastest response time)
- **Creative tasks** → Gemini (creative capabilities)
- **Technical/code** → OpenAI (technical expertise)
- **Multi-model access** → OpenRouter (model diversity)

## 📚 Documentation

- [Installation Guide](./docs/installation.md)
- [API Reference](./docs/api-reference.md)
- [Provider Configuration](./docs/providers.md)
- [Integration Examples](./docs/examples.md)
- [Contributing Guide](./CONTRIBUTING.md)

## 🤝 Contributing

UAUI is open source and welcomes contributions! See [CONTRIBUTING.md](./CONTRIBUTING.md) for guidelines.

## 📄 License

MIT License - see [LICENSE](./LICENSE) for details.

## 🆘 Support

- [Documentation](./docs/)
- [GitHub Issues](https://github.com/uaui-protocol/uaui/issues)
- [Discord Community](https://discord.gg/uaui)

---

**UAUI Protocol** - Making AI provider management intelligent and effortless.

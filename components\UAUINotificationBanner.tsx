/**
 * UAUI Notification Banner
 * Shows important UAUI system notifications and status updates
 */

"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Sparkles,
  X,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Info,
  Beaker
} from 'lucide-react';
import { useUAUI } from '../hooks/use-uaui';
import { useAIProviderStore } from '@/stores/ai-provider-store';

export default function UAUINotificationBanner() {
  const [isDismissed, setIsDismissed] = useState(false);
  const { isInitialized, error, availableProviders } = useUAUI();
  const { providers } = useAIProviderStore();

  const activeProviders = Array.isArray(providers) ? providers.filter(p => p.isActive && p.isConfigured) : [];
  const hasProviders = activeProviders.length > 0;

  // Don't show banner if dismissed or if everything is working fine
  if (isDismissed || (isInitialized && hasProviders && !error)) {
    return null;
  }

  const getNotificationContent = () => {
    if (error) {
      return {
        type: 'error' as const,
        icon: AlertCircle,
        title: 'UAUI System Error',
        description: 'There was an issue with the UAUI system. Check your configuration.',
        action: { text: 'View System', href: '/dashboard/uaui-system' }
      };
    }

    if (!hasProviders) {
      return {
        type: 'warning' as const,
        icon: Info,
        title: 'Configure AI Providers',
        description: 'Set up your AI providers to start using the UAUI system.',
        action: { text: 'Add Providers', href: '/dashboard/ai-providers' }
      };
    }

    if (!isInitialized) {
      return {
        type: 'info' as const,
        icon: Sparkles,
        title: 'UAUI System Available',
        description: 'Universal AI interface is ready to be initialized.',
        action: { text: 'Initialize', href: '/dashboard/uaui-system' }
      };
    }

    return null;
  };

  const notification = getNotificationContent();
  if (!notification) return null;

  const getAlertVariant = () => {
    switch (notification.type) {
      case 'error': return 'destructive';
      case 'warning': return 'default';
      case 'info': return 'default';
      default: return 'default';
    }
  };

  const getAlertStyles = () => {
    switch (notification.type) {
      case 'error':
        return 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950';
      case 'warning':
        return 'border-yellow-200 bg-yellow-50 dark:border-yellow-800 dark:bg-yellow-950';
      case 'info':
        return 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950';
      default:
        return '';
    }
  };

  const getIconColor = () => {
    switch (notification.type) {
      case 'error': return 'text-red-600 dark:text-red-400';
      case 'warning': return 'text-yellow-600 dark:text-yellow-400';
      case 'info': return 'text-blue-600 dark:text-blue-400';
      default: return 'text-muted-foreground';
    }
  };

  const getTextColor = () => {
    switch (notification.type) {
      case 'error': return 'text-red-800 dark:text-red-200';
      case 'warning': return 'text-yellow-800 dark:text-yellow-200';
      case 'info': return 'text-blue-800 dark:text-blue-200';
      default: return '';
    }
  };

  const IconComponent = notification.icon;

  return (
    <Alert className={`mb-6 ${getAlertStyles()}`} variant={getAlertVariant()}>
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-3 flex-1">
          <IconComponent className={`h-4 w-4 mt-0.5 ${getIconColor()}`} />
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h4 className={`font-semibold text-sm ${getTextColor()}`}>
                {notification.title}
              </h4>
              <Badge variant="outline" className="text-xs">
                UAUI
              </Badge>
            </div>
            <AlertDescription className={getTextColor()}>
              {notification.description}
            </AlertDescription>
            <div className="flex items-center gap-2 mt-3">
              <Link href={notification.action.href}>
                <Button 
                  size="sm" 
                  variant={notification.type === 'error' ? 'destructive' : 'default'}
                  className="gap-2"
                >
                  {notification.action.text}
                  <ArrowRight className="h-3 w-3" />
                </Button>
              </Link>
              {notification.type === 'info' && (
                <Link href="/dashboard/uaui-system/test">
                  <Button size="sm" variant="outline" className="gap-2">
                    <Beaker className="h-3 w-3" />
                    Test
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 hover:bg-transparent"
          onClick={() => setIsDismissed(true)}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </Alert>
  );
}

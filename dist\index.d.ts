/**
 * UAUI System - Main Export File
 * Exports all necessary components and types for integration
 */
export { UAUI, createUAUIFromExisting } from './uaui';
export { UAUICore } from './core';
export { UAUIFactory } from './factory';
export { EventBus } from './event-bus';
export { StateManager } from './state-manager';
export { AppRouter } from './app-router';
export { UniversalAIService, AIProviderAdapter, OpenAIAdapter, ClaudeAdapter, GeminiAdapter, MistralAdapter, GroqAdapter } from './ai-service';
export type { UAUIConfig, UAUIAppConfig, UAUIRequest, UAUIResponse, AIConfig, AIProvider, AIProviderType, RequestContext, ResponseMetadata, UAUIError, ExistingProviderConfig } from './types';
export { Logger } from './utils/logger';
export { createUAUIConfig, validateConfig, mergeConfigs, getDefaultConfig } from './utils/config';
export declare const VERSION = "1.0.0";
export declare const DEFAULT_CONFIG: {
    core: {
        version: string;
        environment: string;
        logLevel: string;
        maxConcurrentRequests: number;
        defaultTimeout: number;
    };
    ai: {
        providers: never[];
        selectionStrategy: string;
        fallbackEnabled: boolean;
        caching: {
            enabled: boolean;
            ttl: number;
            maxSize: number;
        };
    };
    apps: {};
};
//# sourceMappingURL=index.d.ts.map
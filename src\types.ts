/**
 * Universal AI-UI Protocol (UAUI) - Core Types
 * Independent, production-ready protocol for AI-UI interactions
 */

// ============================================================================
// CORE EVENT TYPES
// ============================================================================

export interface UAUIEvent {
  id: string;
  type: UAUIEventType;
  timestamp: number;
  source: EventSource;
  target?: EventTarget;
  data: any;
  metadata?: EventMetadata;
}

export type UAUIEventType =
  // Core protocol events
  | 'ai.request'
  | 'ai.response'
  | 'ai.stream'
  | 'ai.error'
  | 'state.sync'
  | 'state.update'
  | 'user.action'
  | 'system.update'
  | 'cross_app.action'
  | 'cross_app.response'
  // Extensible app-specific events
  | string;

export interface EventSource {
  app: string;
  component?: string;
  user?: string;
  session?: string;
}

export interface EventTarget {
  app: string;
  component?: string;
  broadcast?: boolean;
}

export interface EventMetadata {
  priority?: 'low' | 'normal' | 'high' | 'critical';
  persistent?: boolean;
  crossApp?: boolean;
  retryable?: boolean;
  ttl?: number; // Time to live in milliseconds
}

// ============================================================================
// AI REQUEST/RESPONSE TYPES
// ============================================================================

export interface UAUIRequest {
  id: string;
  type: 'ai.request' | 'cross_app.action' | 'state.sync';
  message?: string;
  context: RequestContext;
  metadata?: RequestMetadata;
}

export interface UAUIResponse {
  id: string;
  requestId: string;
  message?: string;
  actions?: UAUIAction[];
  crossAppAction?: CrossAppAction;
  data?: any;
  metadata: ResponseMetadata;
  error?: UAUIError;
}

export interface RequestContext {
  app: string;
  component?: string;
  user?: UserContext;
  session?: SessionContext;
  history?: HistoryContext;
  preferences?: PreferencesContext;
  [key: string]: any; // Extensible for app-specific context
}

export interface UserContext {
  id?: string;
  role?: string;
  permissions?: string[];
  preferences?: Record<string, any>;
}

export interface SessionContext {
  id: string;
  startTime: number;
  lastActivity: number;
  interactions: number;
}

export interface HistoryContext {
  messages?: any[];
  actions?: UAUIAction[];
  states?: any[];
  limit?: number;
}

export interface PreferencesContext {
  aiProvider?: string;
  responseStyle?: 'concise' | 'detailed' | 'technical';
  language?: string;
  timezone?: string;
}

export interface RequestMetadata {
  priority?: 'low' | 'normal' | 'high';
  timeout?: number;
  retries?: number;
  streaming?: boolean;
}

export interface ResponseMetadata {
  provider: string;
  model: string;
  tokens?: number;
  responseTime: number;
  cost?: number;
  timestamp: number;
  cached?: boolean;
}

// ============================================================================
// ACTION TYPES
// ============================================================================

export interface UAUIAction {
  id: string;
  type: UAUIActionType;
  target: string;
  payload: any;
  metadata?: ActionMetadata;
}

export type UAUIActionType =
  | 'ui.update'
  | 'ui.navigate'
  | 'ui.notify'
  | 'state.change'
  | 'state.persist'
  | 'cross_app.navigate'
  | 'cross_app.sync'
  | 'ai.followup'
  | 'ai.suggest'
  | 'system.log'
  | 'system.track'
  // Extensible for app-specific actions
  | string;

export interface ActionMetadata {
  priority?: 'low' | 'normal' | 'high';
  delay?: number;
  condition?: string;
  rollback?: UAUIAction;
}

export interface CrossAppAction {
  targetApp: string;
  action: UAUIAction;
  context?: any;
  callback?: boolean;
}

// ============================================================================
// AI PROVIDER TYPES
// ============================================================================

export interface AIProvider {
  id: string;
  name: string;
  type: AIProviderType;
  config: AIProviderConfig;
  capabilities: AICapability[];
  status: AIProviderStatus;
  metrics: AIProviderMetrics;
}

export type AIProviderType =
  | 'openai'
  | 'claude'
  | 'gemini'
  | 'mistral'
  | 'groq'
  | 'openrouter'
  | 'custom';

export interface AIProviderConfig {
  apiKey: string;
  baseUrl?: string;
  models: string[];
  defaultModel: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
  retries?: number;
}

export interface AICapability {
  type: 'chat' | 'completion' | 'embedding' | 'image' | 'audio' | 'code' | 'analysis';
  models: string[];
  maxTokens?: number;
  streaming?: boolean;
}

export interface AIProviderStatus {
  available: boolean;
  lastChecked: number;
  latency?: number;
  errorRate?: number;
  rateLimit?: {
    remaining: number;
    resetTime: number;
  };
}

export interface AIProviderMetrics {
  totalRequests: number;
  successRate: number;
  averageLatency: number;
  totalTokens: number;
  totalCost: number;
  lastUsed: number;
}

// ============================================================================
// APP CONFIGURATION TYPES
// ============================================================================

export interface UAUIAppConfig {
  id: string;
  name: string;
  type: string;
  version: string;
  capabilities: AppCapability[];
  eventHandlers: EventHandlerConfig[];
  stateSchema?: any;
  crossAppEnabled?: boolean;
}

export interface AppCapability {
  type: string;
  version: string;
  config?: any;
}

export interface EventHandlerConfig {
  eventType: UAUIEventType;
  handler: string;
  priority?: number;
  async?: boolean;
}

// ============================================================================
// ERROR TYPES
// ============================================================================

export interface UAUIError {
  code: string;
  message: string;
  details?: any;
  timestamp: number;
  recoverable?: boolean;
  retryAfter?: number;
}

export type UAUIErrorCode =
  | 'INVALID_REQUEST'
  | 'AI_PROVIDER_ERROR'
  | 'NETWORK_ERROR'
  | 'TIMEOUT_ERROR'
  | 'RATE_LIMIT_ERROR'
  | 'AUTHENTICATION_ERROR'
  | 'PERMISSION_ERROR'
  | 'VALIDATION_ERROR'
  | 'INTERNAL_ERROR';

// ============================================================================
// CONFIGURATION TYPES
// ============================================================================

export interface UAUIConfig {
  core: CoreConfig;
  ai: AIConfig;
  apps: Record<string, UAUIAppConfig>;
  transport?: TransportConfig;
  security?: SecurityConfig;
  monitoring?: MonitoringConfig;
}

export interface CoreConfig {
  version: string;
  environment: 'development' | 'staging' | 'production';
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  maxConcurrentRequests?: number;
  defaultTimeout?: number;
}

export interface AIConfig {
  providers: AIProvider[];
  defaultProvider?: string;
  selectionStrategy: 'round_robin' | 'least_latency' | 'smart' | 'custom';
  fallbackEnabled?: boolean;
  caching?: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
}

export interface TransportConfig {
  type: 'http' | 'websocket' | 'sse' | 'custom';
  config: any;
}

export interface SecurityConfig {
  authentication: {
    required: boolean;
    type: 'jwt' | 'api_key' | 'oauth' | 'custom';
    config: any;
  };
  encryption?: {
    enabled: boolean;
    algorithm: string;
  };
  rateLimit?: {
    enabled: boolean;
    requests: number;
    window: number;
  };
}

export interface MonitoringConfig {
  enabled: boolean;
  metrics: string[];
  alerts?: AlertConfig[];
}

export interface AlertConfig {
  metric: string;
  threshold: number;
  action: string;
}

// ============================================================================
// TEMPO WIDGET INTEGRATION TYPES
// ============================================================================

export interface ExistingProviderConfig {
  id: string;
  name: string;
  type: AIProviderType;
  apiKey: string;
  defaultModel: string;
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
}

export interface TempoWidgetConfig {
  widgetId: string;
  appearance: {
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
  };
  behavior: {
    initialMessage: string;
    showTimestamp: boolean;
  };
  aiSettings: {
    provider: string;
    model: string;
  };
}

export interface TempoAIProvider {
  id: string;
  name: string;
  type: AIProviderType;
  apiKey: string;
  isActive: boolean;
  isConfigured: boolean;
  models: string[];
  lastTested: string | null;
}

export interface TempoAPIClient {
  get: (url: string) => Promise<any>;
  post: (url: string, data: any) => Promise<any>;
  put: (url: string, data: any) => Promise<any>;
  delete: (url: string) => Promise<any>;
}

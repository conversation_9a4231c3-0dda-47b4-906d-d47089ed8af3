{"version": 3, "file": "core.d.ts", "sourceRoot": "", "sources": ["../src/core.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAC;AACtC,OAAO,EACL,UAAU,EAEV,WAAW,EACX,YAAY,EAEZ,cAAc,EACd,aAAa,EAKd,MAAM,SAAS,CAAC;AACjB,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAC;AAGvC,OAAO,EAAE,kBAAkB,EAAE,MAAM,cAAc,CAAC;AAGlD,qBAAa,QAAS,SAAQ,YAAY;IACxC,OAAO,CAAC,MAAM,CAAa;IAC3B,OAAO,CAAC,QAAQ,CAAW;IAC3B,OAAO,CAAC,YAAY,CAAe;IACnC,OAAO,CAAC,SAAS,CAAY;IAC7B,OAAO,CAAC,SAAS,CAAqB;IACtC,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,IAAI,CAA8B;IAC1C,OAAO,CAAC,aAAa,CAAS;gBAElB,MAAM,EAAE,UAAU;IAU9B;;OAEG;IACG,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IA+BjC;;OAEG;IACG,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC;IAiBzE;;OAEG;IACG,gBAAgB,CAAC,OAAO,EAAE,WAAW,GAAG,OAAO,CAAC,YAAY,CAAC;IAoFnE;;OAEG;IACG,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC;IAqB1E;;OAEG;IACG,qBAAqB,CAAC,cAAc,EAAE,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC;IA0B1E;;OAEG;IACH,MAAM,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO,GAAG,SAAS;IAI1C;;OAEG;IACH,OAAO,IAAI,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC;IAI/B;;OAEG;IACH,SAAS,IAAI,UAAU;IAIvB;;OAEG;IACH,YAAY,IAAI,kBAAkB;IAIlC;;OAEG;IACH,WAAW,IAAI,QAAQ;IAIvB;;OAEG;IACG,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;IA0B/B,OAAO,CAAC,kBAAkB;YAMZ,eAAe;YAKf,oBAAoB;YAKpB,eAAe;IAK7B,OAAO,CAAC,eAAe;YAMT,aAAa;YAwBb,WAAW;CAK1B;AAED;;GAEG;AACH,qBAAa,OAAO;IAClB,OAAO,CAAC,EAAE,CAAS;IACnB,OAAO,CAAC,MAAM,CAAgB;IAC9B,OAAO,CAAC,IAAI,CAAW;IACvB,OAAO,CAAC,aAAa,CAAS;gBAElB,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,QAAQ;IAMvD,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAW3B,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;IAI/B,KAAK,IAAI,MAAM;IAIf,SAAS,IAAI,aAAa;YAIZ,WAAW;CAI1B"}
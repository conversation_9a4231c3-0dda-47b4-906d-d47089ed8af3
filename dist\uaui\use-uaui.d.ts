/**
 * React Hook for UAUI Integration
 * Provides easy access to UAUI functionality in React components
 */
import { type ChatMessage } from './uaui-integration-service';
import type { UAUIResponse } from '..';
interface UseUAUIOptions {
    autoInitialize?: boolean;
    environment?: 'development' | 'staging' | 'production';
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
}
interface UseUAUIReturn {
    isInitialized: boolean;
    isLoading: boolean;
    error: string | null;
    availableProviders: string[];
    initialize: () => Promise<boolean>;
    processChatMessage: (message: ChatMessage) => Promise<UAUIResponse | null>;
    reinitialize: () => Promise<boolean>;
    shutdown: () => Promise<void>;
    clearError: () => void;
    isReady: () => boolean;
    getConfig: () => any;
}
export declare function useUAUI(options?: UseUAUIOptions): UseUAUIReturn;
/**
 * Hook for simple UAUI chat processing
 */
export declare function useUAUIChat(widgetId: string): {
    sendMessage: any;
    isInitialized: boolean;
    isLoading: boolean;
    error: string | null;
    availableProviders: string[];
    initialize: () => Promise<boolean>;
    processChatMessage: (message: ChatMessage) => Promise<UAUIResponse | null>;
    reinitialize: () => Promise<boolean>;
    shutdown: () => Promise<void>;
    clearError: () => void;
    isReady: () => boolean;
    getConfig: () => any;
};
export default useUAUI;
//# sourceMappingURL=use-uaui.d.ts.map
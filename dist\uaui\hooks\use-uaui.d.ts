/**
 * UAUI React Hook
 * Provides React integration for the Universal AI User Interface system
 */
import { type ChatMessage } from '../services/uaui-integration-service';
import type { UAUIResponse } from '../..';
interface UseUAUIOptions {
    autoInitialize?: boolean;
    environment?: 'development' | 'production';
    logLevel?: 'debug' | 'info' | 'warn' | 'error';
}
interface UseUAUIReturn {
    isInitialized: boolean;
    isLoading: boolean;
    error: string | null;
    availableProviders: string[];
    initialize: () => Promise<boolean>;
    reinitialize: () => Promise<boolean>;
    shutdown: () => Promise<void>;
    clearError: () => void;
    sendMessage: (message: ChatMessage) => Promise<UAUIResponse | null>;
    isReady: () => boolean;
    getStatus: () => {
        initialized: boolean;
        loading: boolean;
        error: string | null;
        providers: string[];
    };
}
/**
 * React hook for UAUI integration
 */
export declare function useUAUI(options?: UseUAUIOptions): UseUAUIReturn;
export {};
//# sourceMappingURL=use-uaui.d.ts.map